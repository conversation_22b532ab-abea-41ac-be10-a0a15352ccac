# E1Dividas - Sistema de Controle Financeiro

Sistema financeiro SaaS desenvolvido em PHP e MySQL para controle de dívidas e rendas pessoais.

## 🚀 Características

- **Sistema Multi-tenant**: Cada usuário possui seu próprio ambiente isolado
- **Dashboard Interativo**: Gráficos e estatísticas em tempo real
- **Gestão de Dívidas**: Cadastro, controle e quitação de dívidas
- **Controle de Renda**: Gerenciamento de fontes de renda (fixas e variáveis)
- **Design Responsivo**: Interface adaptável para desktop e mobile
- **Cores Personalizadas**: Paleta de cores profissional (#FE7743, #273F4F, #447D9B, #D7D7D7)

## 📋 Requisitos

- PHP 7.4 ou superior
- MySQL 5.7 ou superior
- Extensão PDO habilitada
- Servidor web (Apache/Nginx)

## 🛠️ Instalação

### Método 1: Instalador Automático

1. **<PERSON><PERSON> ou baixe os arquivos** para seu servidor web

2. **Execute o instalador** acessando:
   ```
   http://seudominio.com/install.php
   ```

3. **Siga os passos do instalador**:
   - Configure as credenciais do banco de dados
   - Crie a conta do administrador
   - Finalize a instalação

4. **Remova o arquivo de instalação** por segurança:
   ```bash
   rm install.php
   ```

### Método 2: Instalação Manual (se houver problemas)

1. **Configure o banco de dados**:
   - Copie `config_exemplo.php` para `config/database.php`
   - Edite `config/database.php` com suas credenciais

2. **Execute a instalação manual**:
   - Edite `install_manual.php` com suas configurações
   - Acesse `http://seudominio.com/install_manual.php`
   - Remova o arquivo após a instalação

### Método 3: Instalação via SQL

1. **Importe o banco**:
   ```sql
   -- Execute o conteúdo de database/schema.sql no seu MySQL
   ```

2. **Configure o arquivo**:
   - Copie `config_exemplo.php` para `config/database.php`
   - Edite com suas credenciais

3. **Crie usuário admin**:
   ```sql
   INSERT INTO usuarios (nome, email, senha) VALUES
   ('Admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');
   -- Senha padrão: password
   ```

## 📁 Estrutura do Projeto

```
e1dividas/
├── config/
│   └── database.php          # Configurações do banco
├── database/
│   └── schema.sql           # Estrutura do banco de dados
├── includes/
│   ├── auth.php            # Sistema de autenticação
│   ├── header.php          # Cabeçalho das páginas
│   └── footer.php          # Rodapé das páginas
├── dashboard.php           # Página principal
├── dividas.php            # Gestão de dívidas
├── rendas.php             # Gestão de rendas
├── perfil.php             # Perfil do usuário
├── login.php              # Login/Registro
├── logout.php             # Logout
├── index.php              # Redirecionamento inicial
├── install.php            # Instalador (remover após uso)
└── README.md              # Este arquivo
```

## 🎨 Paleta de Cores

- **Primária**: #FE7743 (Laranja vibrante)
- **Secundária**: #273F4F (Azul escuro)
- **Accent**: #447D9B (Azul médio)
- **Light**: #D7D7D7 (Cinza claro)

## 📊 Funcionalidades

### Dashboard
- Visão geral das finanças
- Gráficos de dívidas por categoria
- Estatísticas de dívidas quitadas vs pendentes
- Resumo financeiro com saldo disponível

### Gestão de Dívidas
- Cadastro de dívidas com categoria e vencimento
- Controle de status (pendente/quitada)
- Cálculo automático de totais
- Listagem com filtros e ordenação

### Controle de Renda
- Cadastro de fontes de renda
- Tipos: fixo ou variável
- Frequências: mensal, semanal, anual, único
- Cálculo automático da renda mensal equivalente

### Sistema de Usuários
- Registro e login seguro
- Perfil editável
- Isolamento de dados por usuário
- Sessões seguras

## 🔧 Configuração

### Banco de Dados
O sistema cria automaticamente as seguintes tabelas:
- `usuarios`: Dados dos usuários
- `dividas`: Registro de dívidas
- `rendas`: Fontes de renda

### Segurança
- Senhas criptografadas com password_hash()
- Proteção contra SQL Injection
- Validação de dados de entrada
- Sessões seguras

## 📱 Responsividade

O sistema é totalmente responsivo, utilizando Bootstrap 5 para garantir uma experiência consistente em:
- Desktop
- Tablets
- Smartphones

## 🎯 Como Usar

1. **Acesse o sistema** através do login
2. **Configure suas rendas** na página "Minha Renda"
3. **Cadastre suas dívidas** na página "Minhas Dívidas"
4. **Acompanhe o progresso** no Dashboard
5. **Quite dívidas** conforme necessário
6. **Monitore seu saldo** disponível

## � Troubleshooting

### Erro: "Table 'usuarios' doesn't exist"
1. Use o `install_manual.php` em vez do `install.php`
2. Verifique se o banco de dados existe
3. Confirme as permissões do usuário MySQL

### Erro de Conexão com Banco
1. Verifique as credenciais em `config/database.php`
2. Teste a conexão MySQL separadamente
3. Confirme se o servidor MySQL está rodando

### Problemas de Permissão
1. Verifique permissões da pasta (755)
2. Confirme permissões de escrita em `config/`
3. Verifique se o PHP pode criar arquivos

### Página em Branco
1. Ative a exibição de erros PHP
2. Verifique logs do servidor
3. Confirme se todas as extensões PHP estão instaladas

## �🔄 Atualizações

Para atualizar o sistema:
1. Faça backup do banco de dados
2. Substitua os arquivos (exceto config/database.php)
3. Execute possíveis scripts de migração

## 📞 Suporte

Para suporte técnico ou dúvidas sobre o sistema, consulte a documentação ou entre em contato com o desenvolvedor.

## 📄 Licença

Este projeto é proprietário. Todos os direitos reservados.

---

**E1Dividas** - Sistema de Controle Financeiro
Desenvolvido com ❤️ em PHP
