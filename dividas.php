<?php
// Debug para erro 500
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

$message = '';
$message_type = '';

// Processar ações
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $titulo = trim($_POST['titulo']);
                $descricao = trim($_POST['descricao']);
                $valor = str_replace(['R$', ' ', '.', ','], ['', '', '', '.'], $_POST['valor']);
                $data_vencimento = $_POST['data_vencimento'] ?: null;
                $categoria = trim($_POST['categoria']);
                
                if (empty($titulo) || empty($valor)) {
                    $message = 'Título e valor são obrigatórios.';
                    $message_type = 'danger';
                } else {
                    $query = "INSERT INTO dividas (usuario_id, titulo, descricao, valor, data_vencimento, categoria) 
                             VALUES (:user_id, :titulo, :descricao, :valor, :data_vencimento, :categoria)";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->bindParam(':titulo', $titulo);
                    $stmt->bindParam(':descricao', $descricao);
                    $stmt->bindParam(':valor', $valor);
                    $stmt->bindParam(':data_vencimento', $data_vencimento);
                    $stmt->bindParam(':categoria', $categoria);
                    
                    if ($stmt->execute()) {
                        $message = 'Dívida cadastrada com sucesso!';
                        $message_type = 'success';
                    } else {
                        $message = 'Erro ao cadastrar dívida.';
                        $message_type = 'danger';
                    }
                }
                break;
                
            case 'toggle_quitada':
                $divida_id = $_POST['divida_id'];
                $quitada = $_POST['quitada'] == '1' ? 0 : 1;
                $data_quitacao = $quitada ? date('Y-m-d H:i:s') : null;
                
                $query = "UPDATE dividas SET quitada = :quitada, data_quitacao = :data_quitacao 
                         WHERE id = :id AND usuario_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':quitada', $quitada);
                $stmt->bindParam(':data_quitacao', $data_quitacao);
                $stmt->bindParam(':id', $divida_id);
                $stmt->bindParam(':user_id', $user_id);
                
                if ($stmt->execute()) {
                    $status = $quitada ? 'quitada' : 'reaberta';
                    $message = "Dívida {$status} com sucesso!";
                    $message_type = 'success';
                }
                break;
                
            case 'edit':
                $divida_id = $_POST['divida_id'];
                $titulo = trim($_POST['titulo']);
                $descricao = trim($_POST['descricao']);
                $valor = str_replace(['R$', ' ', '.', ','], ['', '', '', '.'], $_POST['valor']);
                $data_vencimento = $_POST['data_vencimento'] ?: null;
                $categoria = trim($_POST['categoria']);

                if (empty($titulo) || empty($valor)) {
                    $message = 'Título e valor são obrigatórios.';
                    $message_type = 'danger';
                } else {
                    $query = "UPDATE dividas SET titulo = :titulo, descricao = :descricao, valor = :valor,
                             data_vencimento = :data_vencimento, categoria = :categoria
                             WHERE id = :id AND usuario_id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':titulo', $titulo);
                    $stmt->bindParam(':descricao', $descricao);
                    $stmt->bindParam(':valor', $valor);
                    $stmt->bindParam(':data_vencimento', $data_vencimento);
                    $stmt->bindParam(':categoria', $categoria);
                    $stmt->bindParam(':id', $divida_id);
                    $stmt->bindParam(':user_id', $user_id);

                    if ($stmt->execute()) {
                        $message = 'Dívida atualizada com sucesso!';
                        $message_type = 'success';
                    } else {
                        $message = 'Erro ao atualizar dívida.';
                        $message_type = 'danger';
                    }
                }
                break;

            case 'fechamento_mensal':
                $mes_referencia = $_POST['mes_referencia'];

                // Validar mês de referência
                if (empty($mes_referencia) || !preg_match('/^\d{4}-\d{2}$/', $mes_referencia)) {
                    $message = 'Mês de referência inválido.';
                    $message_type = 'danger';
                    break;
                }

                try {
                    // Criar tabela de históricos se não existir (fora da transação)
                    $db->exec("CREATE TABLE IF NOT EXISTS historicos (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        usuario_id INT NOT NULL,
                        mes_referencia VARCHAR(7) NOT NULL,
                        total_dividas DECIMAL(10,2) DEFAULT 0,
                        total_pendente DECIMAL(10,2) DEFAULT 0,
                        total_quitado DECIMAL(10,2) DEFAULT 0,
                        dividas_pendentes INT DEFAULT 0,
                        dividas_quitadas INT DEFAULT 0,
                        total_rendas DECIMAL(10,2) DEFAULT 0,
                        quantidade_rendas INT DEFAULT 0,
                        data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )");

                    // Verificar se já existe fechamento para este mês
                    $query = "SELECT COUNT(*) as total FROM historicos WHERE usuario_id = :user_id AND mes_referencia = :mes_referencia";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->bindParam(':mes_referencia', $mes_referencia);
                    $stmt->execute();
                    $existe_fechamento = $stmt->fetch(PDO::FETCH_ASSOC)['total'] > 0;

                    if ($existe_fechamento) {
                        $message = 'Já existe um fechamento para este mês.';
                        $message_type = 'warning';
                        break;
                    }

                    // Iniciar transação após verificações
                    $db->beginTransaction();

                    // Buscar dados do mês para histórico
                    $query = "SELECT
                        COUNT(*) as total_dividas,
                        COALESCE(SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END), 0) as total_pendente,
                        COALESCE(SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END), 0) as total_quitado,
                        COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
                        COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas
                    FROM dividas WHERE usuario_id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();
                    $dados_dividas = $stmt->fetch(PDO::FETCH_ASSOC);

                    // Buscar dados das rendas
                    $query = "SELECT
                        COUNT(*) as total_rendas,
                        COALESCE(SUM(valor), 0) as total_renda_mensal
                    FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();
                    $dados_rendas = $stmt->fetch(PDO::FETCH_ASSOC);

                    // Tratar valores NULL para evitar erros
                    $total_pendente = $dados_dividas['total_pendente'] ?? 0;
                    $total_quitado = $dados_dividas['total_quitado'] ?? 0;
                    $dividas_pendentes = $dados_dividas['dividas_pendentes'] ?? 0;
                    $dividas_quitadas = $dados_dividas['dividas_quitadas'] ?? 0;
                    $total_rendas = $dados_rendas['total_renda_mensal'] ?? 0;
                    $quantidade_rendas = $dados_rendas['total_rendas'] ?? 0;

                    // Inserir histórico
                    $total_dividas = $total_pendente + $total_quitado;

                    $query = "INSERT INTO historicos
                        (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
                         dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas)
                        VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                                :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas)";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->bindParam(':mes_referencia', $mes_referencia);
                    $stmt->bindParam(':total_dividas', $total_dividas);
                    $stmt->bindParam(':total_pendente', $total_pendente);
                    $stmt->bindParam(':total_quitado', $total_quitado);
                    $stmt->bindParam(':dividas_pendentes', $dividas_pendentes);
                    $stmt->bindParam(':dividas_quitadas', $dividas_quitadas);
                    $stmt->bindParam(':total_rendas', $total_rendas);
                    $stmt->bindParam(':quantidade_rendas', $quantidade_rendas);
                    
                    if (!$stmt->execute()) {
                        throw new Exception('Erro ao inserir histórico: ' . implode(', ', $stmt->errorInfo()));
                    }

                    // Alterar todas as dívidas quitadas para pendente e atualizar data de vencimento
                    $data_vencimento_atual = date('Y-m-d');
                    $query = "UPDATE dividas SET
                        quitada = 0,
                        data_quitacao = NULL,
                        data_vencimento = :data_vencimento
                        WHERE usuario_id = :user_id AND quitada = 1";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->bindParam(':data_vencimento', $data_vencimento_atual);
                    
                    if (!$stmt->execute()) {
                        throw new Exception('Erro ao atualizar dívidas: ' . implode(', ', $stmt->errorInfo()));
                    }

                    $db->commit();

                    $message = 'Fechamento mensal realizado com sucesso! Histórico salvo, dívidas resetadas e datas de vencimento atualizadas para o mês atual.';
                    $message_type = 'success';

                } catch (Exception $e) {
                    try {
                        if ($db->inTransaction()) {
                            $db->rollback();
                        }
                    } catch (Exception $rollbackError) {
                        error_log("Erro no rollback: " . $rollbackError->getMessage());
                    }

                    // Log detalhado do erro
                    error_log("Erro no fechamento mensal - User ID: $user_id - Erro: " . $e->getMessage());
                    error_log("Stack trace: " . $e->getTraceAsString());

                    $message = 'Erro ao realizar fechamento: ' . $e->getMessage();
                    $message_type = 'danger';
                }
                break;

            case 'delete':
                $divida_id = $_POST['divida_id'];

                $query = "DELETE FROM dividas WHERE id = :id AND usuario_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $divida_id);
                $stmt->bindParam(':user_id', $user_id);

                if ($stmt->execute()) {
                    $message = 'Dívida excluída com sucesso!';
                    $message_type = 'success';
                }
                break;
        }
    }
}

// Buscar todas as dívidas do usuário
$query = "SELECT * FROM dividas WHERE usuario_id = :user_id ORDER BY quitada ASC, data_vencimento ASC, data_criacao DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$dividas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calcular totais
$total_dividas = 0;
$total_quitadas = 0;
$total_pendentes = 0;
$count_quitadas = 0;
$count_pendentes = 0;

foreach ($dividas as $divida) {
    $total_dividas += $divida['valor'];
    if ($divida['quitada']) {
        $total_quitadas += $divida['valor'];
        $count_quitadas++;
    } else {
        $total_pendentes += $divida['valor'];
        $count_pendentes++;
    }
}

$page_title = 'Minhas Dívidas';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-credit-card me-2"></i>Minhas Dívidas</h1>
            <div>
                <button class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#fechamentoModal">
                    <i class="fas fa-calendar-check me-2"></i>Fechamento Mensal
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDividaModal">
                    <i class="fas fa-plus me-2"></i>Nova Dívida
                </button>
            </div>
        </div>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Botão Hide/Show Values -->
<div class="row mb-3">
    <div class="col-12 text-end">
        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleValues">
            <i class="fas fa-eye" id="toggleIcon"></i>
            <span id="toggleText">Ocultar Valores</span>
        </button>
    </div>
</div>

<!-- Cards de Resumo -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #FE7743, #e5663a);">
            <div class="card-body">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Pendentes</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($total_pendentes, 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0"><?php echo $count_pendentes; ?> dívida(s)</p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #28a745, #20c997);">
            <div class="card-body">
                <h5><i class="fas fa-check-circle me-2"></i>Quitadas</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($total_quitadas, 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0"><?php echo $count_quitadas; ?> dívida(s)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #273F4F, #495057);">
            <div class="card-body">
                <h5><i class="fas fa-calculator me-2"></i>Total Geral</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($total_dividas, 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0"><?php echo count($dividas); ?> dívida(s)</p>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Dívidas -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista de Dívidas</h5>
    </div>
    <div class="card-body">
        <?php if (empty($dividas)): ?>
            <div class="text-center py-5">
                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Nenhuma dívida cadastrada</h5>
                <p class="text-muted">Clique no botão "Nova Dívida" para começar.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Título</th>
                            <th>Categoria</th>
                            <th>Valor</th>
                            <th>Vencimento</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($dividas as $divida): ?>
                        <tr class="<?php echo $divida['quitada'] ? 'table-success' : ''; ?>">
                            <td>
                                <strong><?php echo htmlspecialchars($divida['titulo']); ?></strong>
                                <?php if ($divida['descricao']): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($divida['descricao']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($divida['categoria']): ?>
                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($divida['categoria']); ?></span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong>
                                    <span class="value-display">R$ <?php echo number_format($divida['valor'], 2, ',', '.'); ?></span>
                                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                                </strong>
                            </td>
                            <td>
                                <?php if ($divida['data_vencimento']): ?>
                                    <?php 
                                    $vencimento = new DateTime($divida['data_vencimento']);
                                    $hoje = new DateTime();
                                    $diff = $hoje->diff($vencimento);
                                    
                                    if (!$divida['quitada'] && $vencimento < $hoje) {
                                        echo '<span class="text-danger">';
                                        echo $vencimento->format('d/m/Y');
                                        echo '<br><small>Vencida</small></span>';
                                    } else {
                                        echo $vencimento->format('d/m/Y');
                                    }
                                    ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($divida['quitada']): ?>
                                    <span class="badge badge-quitada">
                                        <i class="fas fa-check me-1"></i>Quitada
                                    </span>
                                    <?php if ($divida['data_quitacao']): ?>
                                        <br><small class="text-muted">
                                            <?php echo date('d/m/Y', strtotime($divida['data_quitacao'])); ?>
                                        </small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge badge-pendente">
                                        <i class="fas fa-clock me-1"></i>Pendente
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-info" title="Editar"
                                            onclick="editDivida(<?php echo htmlspecialchars(json_encode($divida)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>

                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_quitada">
                                        <input type="hidden" name="divida_id" value="<?php echo $divida['id']; ?>">
                                        <input type="hidden" name="quitada" value="<?php echo $divida['quitada']; ?>">
                                        <button type="submit" class="btn <?php echo $divida['quitada'] ? 'btn-warning' : 'btn-success'; ?>"
                                                title="<?php echo $divida['quitada'] ? 'Reabrir' : 'Quitar'; ?>">
                                            <i class="fas <?php echo $divida['quitada'] ? 'fa-undo' : 'fa-check'; ?>"></i>
                                        </button>
                                    </form>

                                    <form method="POST" style="display: inline;"
                                          onsubmit="return confirmDelete('Tem certeza que deseja excluir esta dívida?')">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="divida_id" value="<?php echo $divida['id']; ?>">
                                        <button type="submit" class="btn btn-danger" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal para Nova Dívida -->
<div class="modal fade" id="addDividaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Nova Dívida</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="titulo" class="form-label">Título *</label>
                        <input type="text" class="form-control" id="titulo" name="titulo" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="descricao" class="form-label">Descrição</label>
                        <textarea class="form-control" id="descricao" name="descricao" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="valor" class="form-label">Valor *</label>
                                <input type="text" class="form-control money-input" id="valor" name="valor" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="data_vencimento" class="form-label">Data de Vencimento</label>
                                <input type="date" class="form-control" id="data_vencimento" name="data_vencimento">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoria" class="form-label">Categoria</label>
                        <input type="text" class="form-control" id="categoria" name="categoria" 
                               placeholder="Ex: Cartão de Crédito, Financiamento, etc.">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Salvar Dívida
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Editar Dívida -->
<div class="modal fade" id="editDividaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Editar Dívida</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editDividaForm">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="divida_id" id="edit_divida_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_titulo" class="form-label">Título *</label>
                        <input type="text" class="form-control" id="edit_titulo" name="titulo" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_descricao" class="form-label">Descrição</label>
                        <textarea class="form-control" id="edit_descricao" name="descricao" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_valor" class="form-label">Valor *</label>
                                <input type="text" class="form-control money-input" id="edit_valor" name="valor" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_data_vencimento" class="form-label">Data de Vencimento</label>
                                <input type="date" class="form-control" id="edit_data_vencimento" name="data_vencimento">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_categoria" class="form-label">Categoria</label>
                        <input type="text" class="form-control" id="edit_categoria" name="categoria">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Fechamento Mensal -->
<div class="modal fade" id="fechamentoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-calendar-check me-2"></i>Fechamento Mensal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    O fechamento mensal irá:
                    <ul class="mb-0 mt-2">
                        <li>Salvar um histórico do mês atual</li>
                        <li>Alterar todas as dívidas quitadas para "Pendente"</li>
                        <li>Atualizar as datas de vencimento para o mês atual</li>
                        <li>Manter as dívidas cadastradas para o próximo mês</li>
                        <li>Registrar totais de gastos e rendas</li>
                    </ul>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="text-danger">Dívidas Pendentes</h5>
                                <h3>
                                    <span class="value-display">R$ <?php echo number_format($total_pendentes, 2, ',', '.'); ?></span>
                                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                                </h3>
                                <small><?php echo $count_pendentes; ?> dívida(s)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="text-success">Dívidas Quitadas</h5>
                                <h3>
                                    <span class="value-display">R$ <?php echo number_format($total_quitadas, 2, ',', '.'); ?></span>
                                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                                </h3>
                                <small><?php echo $count_quitadas; ?> dívida(s)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <label for="mes_referencia" class="form-label">Mês de Referência</label>
                    <input type="month" class="form-control" id="mes_referencia" value="<?php echo date('Y-m'); ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-warning" onclick="realizarFechamento()">
                    <i class="fas fa-calendar-check me-2"></i>Realizar Fechamento
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Função para editar dívida
function editDivida(divida) {
    document.getElementById('edit_divida_id').value = divida.id;
    document.getElementById('edit_titulo').value = divida.titulo;
    document.getElementById('edit_descricao').value = divida.descricao || '';
    document.getElementById('edit_valor').value = 'R$ ' + parseFloat(divida.valor).toFixed(2).replace('.', ',').replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
    document.getElementById('edit_data_vencimento').value = divida.data_vencimento || '';
    document.getElementById('edit_categoria').value = divida.categoria || '';

    const modal = new bootstrap.Modal(document.getElementById('editDividaModal'));
    modal.show();
}

// Função para realizar fechamento mensal
function realizarFechamento() {
    const mesReferencia = document.getElementById('mes_referencia').value;

    if (!mesReferencia) {
        alert('Por favor, selecione o mês de referência.');
        return;
    }

    if (confirm('Tem certeza que deseja realizar o fechamento mensal? Esta ação não pode ser desfeita.')) {
        // Criar formulário para enviar dados
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'fechamento_mensal';

        const mesInput = document.createElement('input');
        mesInput.type = 'hidden';
        mesInput.name = 'mes_referencia';
        mesInput.value = mesReferencia;

        form.appendChild(actionInput);
        form.appendChild(mesInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Sistema de Hide/Show Values - Versão Simplificada
function toggleValuesVisibility() {
    const valueDisplays = document.querySelectorAll('.value-display');
    const valueHiddens = document.querySelectorAll('.value-hidden');
    const toggleIcon = document.getElementById('toggleIcon');
    const toggleText = document.getElementById('toggleText');

    if (valueDisplays.length === 0) return;

    const isHidden = valueDisplays[0].style.display === 'none';

    if (isHidden) {
        valueDisplays.forEach(el => el.style.display = 'inline');
        valueHiddens.forEach(el => el.style.display = 'none');
        if (toggleIcon) toggleIcon.className = 'fas fa-eye';
        if (toggleText) toggleText.textContent = 'Ocultar Valores';
        localStorage.setItem('valuesHidden', 'false');
    } else {
        valueDisplays.forEach(el => el.style.display = 'none');
        valueHiddens.forEach(el => el.style.display = 'inline');
        if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
        if (toggleText) toggleText.textContent = 'Mostrar Valores';
        localStorage.setItem('valuesHidden', 'true');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Aplicar estado salvo
    const valuesHidden = localStorage.getItem('valuesHidden') === 'true';
    if (valuesHidden) {
        const valueDisplays = document.querySelectorAll('.value-display');
        const valueHiddens = document.querySelectorAll('.value-hidden');
        const toggleIcon = document.getElementById('toggleIcon');
        const toggleText = document.getElementById('toggleText');

        if (valueDisplays.length > 0) {
            valueDisplays.forEach(el => el.style.display = 'none');
            valueHiddens.forEach(el => el.style.display = 'inline');
            if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
            if (toggleText) toggleText.textContent = 'Mostrar Valores';
        }
    }

    // Adicionar listener ao botão
    const toggleButton = document.getElementById('toggleValues');
    if (toggleButton) {
        toggleButton.addEventListener('click', function(e) {
            e.preventDefault();
            toggleValuesVisibility();
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
