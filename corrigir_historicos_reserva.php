<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Correção dos Históricos - Reserva de Emergência</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Usuário logado: ID " . $user_id . "<br>";
    
    // 1. Buscar valor atual da reserva
    echo "<h3>1. Buscando valor atual da reserva</h3>";
    $query = "SELECT COALESCE(valor_atual, 0) as valor_reserva 
             FROM reserva_emergencia WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dados_reserva = $stmt->fetch(PDO::FETCH_ASSOC);
    $valor_reserva_atual = $dados_reserva ? $dados_reserva['valor_reserva'] : 0;
    
    echo "Valor atual da reserva: R$ " . number_format($valor_reserva_atual, 2, ',', '.') . "<br>";
    
    if ($valor_reserva_atual <= 0) {
        echo "⚠ Valor da reserva é zero ou negativo. Não há o que corrigir.<br>";
        exit;
    }
    
    // 2. Verificar históricos com reserva_emergencia = 0
    echo "<h3>2. Verificando históricos com reserva zerada</h3>";
    $query = "SELECT id, mes_referencia, reserva_emergencia, data_fechamento 
             FROM historicos 
             WHERE usuario_id = :user_id AND (reserva_emergencia = 0 OR reserva_emergencia IS NULL)
             ORDER BY data_fechamento DESC";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $historicos_zerados = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($historicos_zerados)) {
        echo "✓ Nenhum histórico com reserva zerada encontrado<br>";
        exit;
    }
    
    echo "Encontrados " . count($historicos_zerados) . " históricos com reserva zerada:<br>";
    foreach ($historicos_zerados as $historico) {
        echo "- ID: " . $historico['id'] . 
             " | Mês: " . $historico['mes_referencia'] . 
             " | Reserva atual: R$ " . number_format($historico['reserva_emergencia'] ?? 0, 2, ',', '.') . 
             " | Data: " . $historico['data_fechamento'] . "<br>";
    }
    
    // 3. Perguntar se deseja corrigir
    if (!isset($_POST['confirmar_correcao'])) {
        echo "<h3>3. Confirmação de Correção</h3>";
        echo "<div class='alert alert-warning'>";
        echo "<p><strong>Atenção:</strong> Deseja atualizar todos esses históricos com o valor atual da reserva?</p>";
        echo "<p>Valor que será aplicado: <strong>R$ " . number_format($valor_reserva_atual, 2, ',', '.') . "</strong></p>";
        echo "</div>";
        
        echo '<form method="POST">';
        echo '<input type="hidden" name="confirmar_correcao" value="1">';
        echo '<button type="submit" class="btn btn-warning">Sim, Corrigir Históricos</button>';
        echo ' <a href="historicos.php" class="btn btn-secondary">Cancelar</a>';
        echo '</form>';
        exit;
    }
    
    // 4. Realizar a correção
    echo "<h3>4. Realizando correção</h3>";
    $db->beginTransaction();
    
    try {
        $historicos_corrigidos = 0;
        
        foreach ($historicos_zerados as $historico) {
            $query = "UPDATE historicos 
                     SET reserva_emergencia = :valor_reserva 
                     WHERE id = :historico_id AND usuario_id = :user_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':valor_reserva', $valor_reserva_atual);
            $stmt->bindParam(':historico_id', $historico['id']);
            $stmt->bindParam(':user_id', $user_id);
            
            if ($stmt->execute()) {
                $historicos_corrigidos++;
                echo "✓ Histórico ID " . $historico['id'] . " (" . $historico['mes_referencia'] . ") corrigido<br>";
            } else {
                echo "❌ Erro ao corrigir histórico ID " . $historico['id'] . "<br>";
            }
        }
        
        $db->commit();
        
        echo "<h3 style='color: green;'>✅ Correção Concluída!</h3>";
        echo "<p><strong>Históricos corrigidos:</strong> " . $historicos_corrigidos . "</p>";
        echo "<p><strong>Valor aplicado:</strong> R$ " . number_format($valor_reserva_atual, 2, ',', '.') . "</p>";
        
        // 5. Verificar resultado
        echo "<h3>5. Verificando resultado</h3>";
        $query = "SELECT id, mes_referencia, reserva_emergencia, data_fechamento 
                 FROM historicos 
                 WHERE usuario_id = :user_id 
                 ORDER BY data_fechamento DESC LIMIT 5";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $historicos_atualizados = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Últimos 5 históricos após correção:<br>";
        foreach ($historicos_atualizados as $historico) {
            echo "- ID: " . $historico['id'] . 
                 " | Mês: " . $historico['mes_referencia'] . 
                 " | Reserva: R$ " . number_format($historico['reserva_emergencia'] ?? 0, 2, ',', '.') . 
                 " | Data: " . $historico['data_fechamento'] . "<br>";
        }
        
        echo "<br><p><a href='historicos.php' class='btn btn-primary'>Ver Históricos Corrigidos</a></p>";
        
    } catch (Exception $e) {
        $db->rollback();
        echo "<h3 style='color: red;'>❌ Erro na Correção</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO GERAL:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
}
?>

<style>
.alert {
    padding: 15px;
    margin: 20px 0;
    border: 1px solid transparent;
    border-radius: 4px;
}
.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}
.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    text-decoration: none;
}
.btn-warning {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #eea236;
}
.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}
</style>
