<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

$message = '';
$message_type = '';

// Processar ações
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $titulo = trim($_POST['titulo']);
                $descricao = trim($_POST['descricao']);
                $valor = str_replace(['R$', ' ', '.', ','], ['', '', '', '.'], $_POST['valor']);
                $tipo = $_POST['tipo'];
                $frequencia = $_POST['frequencia'];
                
                if (empty($titulo) || empty($valor)) {
                    $message = 'Título e valor são obrigatórios.';
                    $message_type = 'danger';
                } else {
                    $query = "INSERT INTO rendas (usuario_id, titulo, descricao, valor, tipo, frequencia) 
                             VALUES (:user_id, :titulo, :descricao, :valor, :tipo, :frequencia)";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->bindParam(':titulo', $titulo);
                    $stmt->bindParam(':descricao', $descricao);
                    $stmt->bindParam(':valor', $valor);
                    $stmt->bindParam(':tipo', $tipo);
                    $stmt->bindParam(':frequencia', $frequencia);
                    
                    if ($stmt->execute()) {
                        $message = 'Fonte de renda cadastrada com sucesso!';
                        $message_type = 'success';
                    } else {
                        $message = 'Erro ao cadastrar fonte de renda.';
                        $message_type = 'danger';
                    }
                }
                break;
                
            case 'toggle_ativo':
                $renda_id = $_POST['renda_id'];
                $ativo = $_POST['ativo'] == '1' ? 0 : 1;
                
                $query = "UPDATE rendas SET ativo = :ativo WHERE id = :id AND usuario_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':ativo', $ativo);
                $stmt->bindParam(':id', $renda_id);
                $stmt->bindParam(':user_id', $user_id);
                
                if ($stmt->execute()) {
                    $status = $ativo ? 'ativada' : 'desativada';
                    $message = "Fonte de renda {$status} com sucesso!";
                    $message_type = 'success';
                }
                break;
                
            case 'delete':
                $renda_id = $_POST['renda_id'];
                
                $query = "DELETE FROM rendas WHERE id = :id AND usuario_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $renda_id);
                $stmt->bindParam(':user_id', $user_id);
                
                if ($stmt->execute()) {
                    $message = 'Fonte de renda excluída com sucesso!';
                    $message_type = 'success';
                }
                break;
        }
    }
}

// Buscar todas as rendas do usuário
$query = "SELECT * FROM rendas WHERE usuario_id = :user_id ORDER BY ativo DESC, data_criacao DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$rendas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calcular totais
$total_renda_mensal = 0;
$total_renda_anual = 0;
$count_ativas = 0;
$count_inativas = 0;

foreach ($rendas as $renda) {
    if ($renda['ativo']) {
        $count_ativas++;
        $valor_mensal = 0;
        
        switch ($renda['frequencia']) {
            case 'mensal':
                $valor_mensal = $renda['valor'];
                break;
            case 'semanal':
                $valor_mensal = $renda['valor'] * 4.33; // média de semanas por mês
                break;
            case 'anual':
                $valor_mensal = $renda['valor'] / 12;
                break;
            case 'unico':
                $valor_mensal = 0; // não conta para renda mensal recorrente
                break;
        }
        
        $total_renda_mensal += $valor_mensal;
        $total_renda_anual += $valor_mensal * 12;
    } else {
        $count_inativas++;
    }
}

$page_title = 'Minha Renda';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-money-bill-wave me-2"></i>Minha Renda</h1>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRendaModal">
                <i class="fas fa-plus me-2"></i>Nova Fonte de Renda
            </button>
        </div>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Botão Hide/Show Values -->
<div class="row mb-3">
    <div class="col-12 text-end">
        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleValues">
            <i class="fas fa-eye" id="toggleIcon"></i>
            <span id="toggleText">Ocultar Valores</span>
        </button>
    </div>
</div>

<!-- Cards de Resumo -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #28a745, #20c997);">
            <div class="card-body">
                <h5><i class="fas fa-calendar-alt me-2"></i>Renda Mensal</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($total_renda_mensal, 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0"><?php echo $count_ativas; ?> fonte(s) ativa(s)</p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #447D9B, #6c757d);">
            <div class="card-body">
                <h5><i class="fas fa-chart-line me-2"></i>Projeção Anual</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($total_renda_anual, 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0">Baseado na renda mensal</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #273F4F, #495057);">
            <div class="card-body">
                <h5><i class="fas fa-list me-2"></i>Total de Fontes</h5>
                <h3><?php echo count($rendas); ?></h3>
                <p class="mb-0"><?php echo $count_ativas; ?> ativas, <?php echo $count_inativas; ?> inativas</p>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Rendas -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Fontes de Renda</h5>
    </div>
    <div class="card-body">
        <?php if (empty($rendas)): ?>
            <div class="text-center py-5">
                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Nenhuma fonte de renda cadastrada</h5>
                <p class="text-muted">Clique no botão "Nova Fonte de Renda" para começar.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Título</th>
                            <th>Valor</th>
                            <th>Tipo</th>
                            <th>Frequência</th>
                            <th>Valor Mensal</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($rendas as $renda): ?>
                        <?php
                        // Calcular valor mensal equivalente
                        $valor_mensal_equiv = 0;
                        switch ($renda['frequencia']) {
                            case 'mensal':
                                $valor_mensal_equiv = $renda['valor'];
                                break;
                            case 'semanal':
                                $valor_mensal_equiv = $renda['valor'] * 4.33;
                                break;
                            case 'anual':
                                $valor_mensal_equiv = $renda['valor'] / 12;
                                break;
                            case 'unico':
                                $valor_mensal_equiv = 0;
                                break;
                        }
                        ?>
                        <tr class="<?php echo !$renda['ativo'] ? 'table-secondary' : ''; ?>">
                            <td>
                                <strong><?php echo htmlspecialchars($renda['titulo']); ?></strong>
                                <?php if ($renda['descricao']): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($renda['descricao']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong>
                                    <span class="value-display">R$ <?php echo number_format($renda['valor'], 2, ',', '.'); ?></span>
                                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                                </strong>
                            </td>
                            <td>
                                <?php if ($renda['tipo'] == 'fixo'): ?>
                                    <span class="badge bg-success">Fixo</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Variável</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $frequencias = [
                                    'mensal' => 'Mensal',
                                    'semanal' => 'Semanal',
                                    'anual' => 'Anual',
                                    'unico' => 'Único'
                                ];
                                echo $frequencias[$renda['frequencia']];
                                ?>
                            </td>
                            <td>
                                <?php if ($renda['frequencia'] != 'unico' && $renda['ativo']): ?>
                                    <strong class="text-success">
                                        <span class="value-display">R$ <?php echo number_format($valor_mensal_equiv, 2, ',', '.'); ?></span>
                                        <span class="value-hidden" style="display: none;">R$ •••,••</span>
                                    </strong>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($renda['ativo']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Ativa
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-pause me-1"></i>Inativa
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_ativo">
                                        <input type="hidden" name="renda_id" value="<?php echo $renda['id']; ?>">
                                        <input type="hidden" name="ativo" value="<?php echo $renda['ativo']; ?>">
                                        <button type="submit" class="btn <?php echo $renda['ativo'] ? 'btn-warning' : 'btn-success'; ?>" 
                                                title="<?php echo $renda['ativo'] ? 'Desativar' : 'Ativar'; ?>">
                                            <i class="fas <?php echo $renda['ativo'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                        </button>
                                    </form>
                                    
                                    <form method="POST" style="display: inline;" 
                                          onsubmit="return confirmDelete('Tem certeza que deseja excluir esta fonte de renda?')">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="renda_id" value="<?php echo $renda['id']; ?>">
                                        <button type="submit" class="btn btn-danger" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal para Nova Fonte de Renda -->
<div class="modal fade" id="addRendaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Nova Fonte de Renda</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="titulo" class="form-label">Título *</label>
                        <input type="text" class="form-control" id="titulo" name="titulo" required
                               placeholder="Ex: Salário, Freelance, Aluguel, etc.">
                    </div>
                    
                    <div class="mb-3">
                        <label for="descricao" class="form-label">Descrição</label>
                        <textarea class="form-control" id="descricao" name="descricao" rows="3"
                                  placeholder="Detalhes sobre esta fonte de renda"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="valor" class="form-label">Valor *</label>
                                <input type="text" class="form-control money-input" id="valor" name="valor" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tipo" class="form-label">Tipo</label>
                                <select class="form-select" id="tipo" name="tipo">
                                    <option value="fixo">Fixo</option>
                                    <option value="variavel">Variável</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="frequencia" class="form-label">Frequência</label>
                        <select class="form-select" id="frequencia" name="frequencia">
                            <option value="mensal">Mensal</option>
                            <option value="semanal">Semanal</option>
                            <option value="anual">Anual</option>
                            <option value="unico">Único (não recorrente)</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Salvar Fonte de Renda
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Sistema de Hide/Show Values - Versão Simplificada
function toggleValuesVisibility() {
    const valueDisplays = document.querySelectorAll('.value-display');
    const valueHiddens = document.querySelectorAll('.value-hidden');
    const toggleIcon = document.getElementById('toggleIcon');
    const toggleText = document.getElementById('toggleText');

    if (valueDisplays.length === 0) return;

    const isHidden = valueDisplays[0].style.display === 'none';

    if (isHidden) {
        valueDisplays.forEach(el => el.style.display = 'inline');
        valueHiddens.forEach(el => el.style.display = 'none');
        if (toggleIcon) toggleIcon.className = 'fas fa-eye';
        if (toggleText) toggleText.textContent = 'Ocultar Valores';
        localStorage.setItem('valuesHidden', 'false');
    } else {
        valueDisplays.forEach(el => el.style.display = 'none');
        valueHiddens.forEach(el => el.style.display = 'inline');
        if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
        if (toggleText) toggleText.textContent = 'Mostrar Valores';
        localStorage.setItem('valuesHidden', 'true');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Aplicar estado salvo
    const valuesHidden = localStorage.getItem('valuesHidden') === 'true';
    if (valuesHidden) {
        const valueDisplays = document.querySelectorAll('.value-display');
        const valueHiddens = document.querySelectorAll('.value-hidden');
        const toggleIcon = document.getElementById('toggleIcon');
        const toggleText = document.getElementById('toggleText');

        if (valueDisplays.length > 0) {
            valueDisplays.forEach(el => el.style.display = 'none');
            valueHiddens.forEach(el => el.style.display = 'inline');
            if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
            if (toggleText) toggleText.textContent = 'Mostrar Valores';
        }
    }

    // Adicionar listener ao botão
    const toggleButton = document.getElementById('toggleValues');
    if (toggleButton) {
        toggleButton.addEventListener('click', function(e) {
            e.preventDefault();
            toggleValuesVisibility();
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
