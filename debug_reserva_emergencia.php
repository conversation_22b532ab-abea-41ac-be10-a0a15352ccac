<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debug Reserva de Emergência no Fechamento</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Usuário logado: ID " . $user_id . "<br>";
    
    // 1. Verificar se a tabela reserva_emergencia existe
    echo "<h3>1. Verificando tabela reserva_emergencia</h3>";
    $query = "SHOW TABLES LIKE 'reserva_emergencia'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "✓ Tabela reserva_emergencia existe<br>";
        
        // Verificar estrutura da tabela
        $query = "DESCRIBE reserva_emergencia";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Estrutura da tabela reserva_emergencia:<br>";
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
        }
    } else {
        echo "❌ Tabela reserva_emergencia NÃO existe<br>";
    }
    
    // 2. Verificar dados da reserva do usuário
    echo "<h3>2. Verificando dados da reserva do usuário</h3>";
    try {
        $query = "SELECT * FROM reserva_emergencia WHERE usuario_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $reserva = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($reserva) {
            echo "✓ Reserva encontrada:<br>";
            echo "- ID: " . $reserva['id'] . "<br>";
            echo "- Valor atual: R$ " . number_format($reserva['valor_atual'], 2, ',', '.') . "<br>";
            echo "- Meta: R$ " . number_format($reserva['meta_valor'], 2, ',', '.') . "<br>";
            echo "- Data criação: " . $reserva['data_criacao'] . "<br>";
            echo "- Data atualização: " . $reserva['data_atualizacao'] . "<br>";
        } else {
            echo "❌ Nenhuma reserva encontrada para o usuário<br>";
        }
    } catch (Exception $e) {
        echo "❌ Erro ao buscar reserva: " . $e->getMessage() . "<br>";
    }
    
    // 3. Testar a query que é usada no fechamento
    echo "<h3>3. Testando query do fechamento</h3>";
    try {
        $query = "SELECT COALESCE(valor_atual, 0) as valor_reserva 
                 FROM reserva_emergencia WHERE usuario_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $dados_reserva = $stmt->fetch(PDO::FETCH_ASSOC);
        $valor_reserva = $dados_reserva ? $dados_reserva['valor_reserva'] : 0;
        
        echo "✓ Query executada com sucesso<br>";
        echo "- Resultado da query: " . ($dados_reserva ? 'Encontrado' : 'Não encontrado') . "<br>";
        echo "- Valor retornado: R$ " . number_format($valor_reserva, 2, ',', '.') . "<br>";
        
        if ($dados_reserva) {
            echo "- Dados completos: " . print_r($dados_reserva, true) . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ Erro na query: " . $e->getMessage() . "<br>";
    }
    
    // 4. Verificar estrutura da tabela historicos
    echo "<h3>4. Verificando tabela historicos</h3>";
    $query = "DESCRIBE historicos";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Estrutura da tabela historicos:<br>";
    $tem_coluna_reserva = false;
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")";
        if ($column['Field'] == 'reserva_emergencia') {
            echo " ✓ COLUNA RESERVA ENCONTRADA";
            $tem_coluna_reserva = true;
        }
        echo "<br>";
    }
    
    if (!$tem_coluna_reserva) {
        echo "<br>❌ COLUNA reserva_emergencia NÃO EXISTE na tabela historicos<br>";
        echo "Tentando adicionar a coluna...<br>";
        
        try {
            $db->exec("ALTER TABLE historicos ADD COLUMN reserva_emergencia DECIMAL(10,2) DEFAULT 0");
            echo "✓ Coluna reserva_emergencia adicionada com sucesso<br>";
        } catch (Exception $e) {
            echo "❌ Erro ao adicionar coluna: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "<br>✓ Coluna reserva_emergencia existe na tabela historicos<br>";
    }
    
    // 5. Verificar históricos existentes
    echo "<h3>5. Verificando históricos existentes</h3>";
    try {
        $query = "SELECT id, mes_referencia, reserva_emergencia, data_fechamento 
                 FROM historicos WHERE usuario_id = :user_id 
                 ORDER BY data_fechamento DESC LIMIT 5";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $historicos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($historicos) {
            echo "Últimos 5 históricos:<br>";
            foreach ($historicos as $historico) {
                echo "- ID: " . $historico['id'] . 
                     " | Mês: " . $historico['mes_referencia'] . 
                     " | Reserva: R$ " . number_format($historico['reserva_emergencia'] ?? 0, 2, ',', '.') . 
                     " | Data: " . $historico['data_fechamento'] . "<br>";
            }
        } else {
            echo "Nenhum histórico encontrado<br>";
        }
    } catch (Exception $e) {
        echo "❌ Erro ao buscar históricos: " . $e->getMessage() . "<br>";
    }
    
    // 6. Simular inserção de teste
    echo "<h3>6. Simulando inserção de teste</h3>";
    if ($valor_reserva > 0) {
        echo "Valor da reserva para teste: R$ " . number_format($valor_reserva, 2, ',', '.') . "<br>";
        
        $mes_teste = date('Y-m') . '-TEST-' . time();
        
        try {
            $query = "INSERT INTO historicos
                (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
                 dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas, reserva_emergencia)
                VALUES (:user_id, :mes_referencia, 100, 50, 50, 1, 1, 1000, 2, :reserva_emergencia)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':mes_referencia', $mes_teste);
            $stmt->bindParam(':reserva_emergencia', $valor_reserva);
            
            if ($stmt->execute()) {
                echo "✓ Inserção de teste realizada com sucesso<br>";
                
                // Verificar se foi inserido corretamente
                $query = "SELECT reserva_emergencia FROM historicos WHERE mes_referencia = :mes_referencia";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':mes_referencia', $mes_teste);
                $stmt->execute();
                $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($resultado) {
                    echo "✓ Valor inserido: R$ " . number_format($resultado['reserva_emergencia'], 2, ',', '.') . "<br>";
                } else {
                    echo "❌ Erro ao verificar inserção<br>";
                }
                
                // Limpar teste
                $query = "DELETE FROM historicos WHERE mes_referencia = :mes_referencia";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':mes_referencia', $mes_teste);
                $stmt->execute();
                echo "✓ Registro de teste removido<br>";
            } else {
                echo "❌ Erro na inserção de teste: " . implode(', ', $stmt->errorInfo()) . "<br>";
            }
        } catch (Exception $e) {
            echo "❌ Erro na simulação: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "⚠ Valor da reserva é zero, não é possível testar inserção<br>";
    }
    
    echo "<h2 style='color: blue;'>📋 RESUMO DO DIAGNÓSTICO</h2>";
    echo "<p><strong>Reserva atual:</strong> R$ " . number_format($valor_reserva, 2, ',', '.') . "</p>";
    echo "<p><strong>Coluna existe:</strong> " . ($tem_coluna_reserva ? 'Sim' : 'Não') . "</p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO GERAL:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
}
?>
