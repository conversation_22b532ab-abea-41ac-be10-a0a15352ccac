<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debug do Saldo na Dashboard</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Usuário logado: ID " . $user_id . "<br>";
    
    // Buscar estatísticas das dívidas (igual ao dashboard)
    echo "<h3>1. Estatísticas das Dívidas</h3>";
    $query = "SELECT 
        COUNT(*) as total_dividas,
        COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas,
        COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
        COALESCE(SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END), 0) as total_quitado,
        COALESCE(SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END), 0) as total_pendente,
        COALESCE(SUM(valor), 0) as total_geral
    FROM dividas WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $stats_dividas = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Dados das dívidas:<br>";
    echo "- Total de dívidas: " . $stats_dividas['total_dividas'] . "<br>";
    echo "- Dívidas quitadas: " . $stats_dividas['dividas_quitadas'] . "<br>";
    echo "- Dívidas pendentes: " . $stats_dividas['dividas_pendentes'] . "<br>";
    echo "- <strong>Total quitado (gastos): R$ " . number_format($stats_dividas['total_quitado'], 2, ',', '.') . "</strong><br>";
    echo "- Total pendente: R$ " . number_format($stats_dividas['total_pendente'], 2, ',', '.') . "<br>";
    echo "- Total geral: R$ " . number_format($stats_dividas['total_geral'], 2, ',', '.') . "<br>";
    
    // Buscar estatísticas das rendas (igual ao dashboard)
    echo "<h3>2. Estatísticas das Rendas</h3>";
    $query = "SELECT 
        COUNT(*) as total_rendas,
        COALESCE(SUM(valor), 0) as total_renda_mensal
    FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $stats_rendas = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Dados das rendas:<br>";
    echo "- Total de rendas ativas: " . $stats_rendas['total_rendas'] . "<br>";
    echo "- <strong>Total renda mensal: R$ " . number_format($stats_rendas['total_renda_mensal'], 2, ',', '.') . "</strong><br>";
    
    // Calcular saldo (igual ao dashboard)
    echo "<h3>3. Cálculo do Saldo</h3>";
    $saldo = ($stats_rendas['total_renda_mensal'] ?? 0) - ($stats_dividas['total_quitado'] ?? 0);
    $classe_saldo = $saldo >= 0 ? 'text-success' : 'text-danger';
    $texto_saldo = $saldo >= 0 ? 'Economia' : 'Déficit';
    
    echo "Fórmula: Rendas - Gastos<br>";
    echo "Cálculo: R$ " . number_format($stats_rendas['total_renda_mensal'], 2, ',', '.') . 
         " - R$ " . number_format($stats_dividas['total_quitado'], 2, ',', '.') . "<br>";
    echo "<strong>Resultado: R$ " . number_format($saldo, 2, ',', '.') . "</strong><br>";
    echo "Status: " . $texto_saldo . "<br>";
    echo "Classe CSS: " . $classe_saldo . "<br>";
    
    // Mostrar como está sendo exibido no dashboard
    echo "<h3>4. Como está sendo exibido no dashboard</h3>";
    echo "Valor com abs(): R$ " . number_format(abs($saldo), 2, ',', '.') . "<br>";
    echo "Valor sem abs(): R$ " . number_format($saldo, 2, ',', '.') . "<br>";
    
    // Verificar se há algum problema nos dados
    echo "<h3>5. Verificação de Consistência</h3>";
    
    if ($stats_rendas['total_renda_mensal'] == 12232.01) {
        echo "⚠ PROBLEMA IDENTIFICADO: O saldo está mostrando o valor total das rendas!<br>";
        echo "Isso indica que o cálculo está errado ou há um problema na exibição.<br>";
    }
    
    if ($saldo == 348.13) {
        echo "✓ Cálculo correto: O saldo calculado bate com o esperado (R$ 348,13)<br>";
    } else {
        echo "❌ Cálculo incorreto: O saldo deveria ser R$ 348,13 mas está R$ " . number_format($saldo, 2, ',', '.') . "<br>";
    }
    
    // Mostrar valores esperados vs atuais
    echo "<h3>6. Comparação de Valores</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Item</th><th>Valor Esperado</th><th>Valor Atual</th><th>Status</th></tr>";
    
    $valores_esperados = [
        'Total Rendas' => 12232.01,
        'Total Gastos' => 11883.88,
        'Saldo (Sobra)' => 348.13
    ];
    
    $valores_atuais = [
        'Total Rendas' => $stats_rendas['total_renda_mensal'],
        'Total Gastos' => $stats_dividas['total_quitado'],
        'Saldo (Sobra)' => $saldo
    ];
    
    foreach ($valores_esperados as $item => $esperado) {
        $atual = $valores_atuais[$item];
        $diferenca = abs($esperado - $atual);
        $status = $diferenca < 0.01 ? '✓ OK' : '❌ DIFERENTE';
        
        echo "<tr>";
        echo "<td>" . $item . "</td>";
        echo "<td>R$ " . number_format($esperado, 2, ',', '.') . "</td>";
        echo "<td>R$ " . number_format($atual, 2, ',', '.') . "</td>";
        echo "<td>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>7. Diagnóstico</h3>";
    if (abs($saldo - 348.13) < 0.01) {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<strong>✓ CÁLCULO CORRETO:</strong><br>";
        echo "O saldo está sendo calculado corretamente como R$ " . number_format($saldo, 2, ',', '.') . "<br>";
        echo "O problema deve estar na exibição no dashboard.<br>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "<strong>❌ PROBLEMA NO CÁLCULO:</strong><br>";
        echo "O saldo deveria ser R$ 348,13 mas está sendo calculado como R$ " . number_format($saldo, 2, ',', '.') . "<br>";
        echo "Verifique os dados das rendas e dívidas.<br>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
}
?>
