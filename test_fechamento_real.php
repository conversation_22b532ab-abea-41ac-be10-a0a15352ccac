<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Teste Real do Fechamento Mensal</h2>";

// Simular exatamente o que o JavaScript faz
$_POST['action'] = 'fechamento_mensal';
$_POST['mes_referencia'] = date('Y-m');

echo "<h3>Dados POST simulados:</h3>";
echo "Action: " . $_POST['action'] . "<br>";
echo "Mês referência: " . $_POST['mes_referencia'] . "<br>";

try {
    // Incluir exatamente como no dividas.php
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    $message = '';
    $message_type = '';
    
    echo "✓ Inicialização OK - User ID: " . $user_id . "<br>";
    
    // Processar ações - exatamente como no dividas.php
    if ($_POST) {
        echo "✓ POST detectado<br>";
        
        if (isset($_POST['action'])) {
            echo "✓ Action detectada: " . $_POST['action'] . "<br>";
            
            switch ($_POST['action']) {
                case 'fechamento_mensal':
                    echo "<h3>Processando fechamento mensal...</h3>";
                    
                    $mes_referencia = $_POST['mes_referencia'];
                    echo "Mês de referência recebido: " . $mes_referencia . "<br>";
                    
                    // Validar mês de referência
                    if (empty($mes_referencia) || !preg_match('/^\d{4}-\d{2}$/', $mes_referencia)) {
                        $message = 'Mês de referência inválido.';
                        $message_type = 'danger';
                        echo "❌ " . $message . "<br>";
                        break;
                    }
                    echo "✓ Validação do mês OK<br>";
                    
                    try {
                        $db->beginTransaction();
                        echo "✓ Transação iniciada<br>";
                        
                        // Verificar se já existe fechamento para este mês
                        $query = "SELECT COUNT(*) as total FROM historicos WHERE usuario_id = :user_id AND mes_referencia = :mes_referencia";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->bindParam(':mes_referencia', $mes_referencia);
                        $stmt->execute();
                        $result = $stmt->fetch(PDO::FETCH_ASSOC);
                        $existe_fechamento = $result['total'] > 0;
                        
                        echo "Verificação de fechamento existente: " . ($existe_fechamento ? 'Existe' : 'Não existe') . "<br>";
                        
                        if ($existe_fechamento) {
                            $message = 'Já existe um fechamento para este mês.';
                            $message_type = 'warning';
                            $db->rollback();
                            echo "⚠ " . $message . "<br>";
                            break;
                        }
                        
                        // Criar tabela de históricos se não existir
                        $create_sql = "CREATE TABLE IF NOT EXISTS historicos (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            usuario_id INT NOT NULL,
                            mes_referencia VARCHAR(7) NOT NULL,
                            total_dividas DECIMAL(10,2) DEFAULT 0,
                            total_pendente DECIMAL(10,2) DEFAULT 0,
                            total_quitado DECIMAL(10,2) DEFAULT 0,
                            dividas_pendentes INT DEFAULT 0,
                            dividas_quitadas INT DEFAULT 0,
                            total_rendas DECIMAL(10,2) DEFAULT 0,
                            quantidade_rendas INT DEFAULT 0,
                            data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )";
                        
                        $db->exec($create_sql);
                        echo "✓ Tabela historicos verificada/criada<br>";
                        
                        // Buscar dados do mês para histórico
                        $query = "SELECT
                            COUNT(*) as total_dividas,
                            COALESCE(SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END), 0) as total_pendente,
                            COALESCE(SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END), 0) as total_quitado,
                            COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
                            COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas
                        FROM dividas WHERE usuario_id = :user_id";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->execute();
                        $dados_dividas = $stmt->fetch(PDO::FETCH_ASSOC);
                        echo "✓ Dados das dívidas coletados<br>";
                        
                        // Buscar dados das rendas
                        $query = "SELECT
                            COUNT(*) as total_rendas,
                            COALESCE(SUM(valor), 0) as total_renda_mensal
                        FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->execute();
                        $dados_rendas = $stmt->fetch(PDO::FETCH_ASSOC);
                        echo "✓ Dados das rendas coletados<br>";

                        // Buscar dados da reserva de emergência
                        $query = "SELECT COALESCE(valor_atual, 0) as valor_reserva
                                 FROM reserva_emergencia WHERE usuario_id = :user_id";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->execute();
                        $dados_reserva = $stmt->fetch(PDO::FETCH_ASSOC);
                        $valor_reserva = $dados_reserva ? $dados_reserva['valor_reserva'] : 0;
                        echo "✓ Dados da reserva de emergência coletados<br>";
                        
                        // Tratar valores NULL para evitar erros
                        $total_pendente = $dados_dividas['total_pendente'] ?? 0;
                        $total_quitado = $dados_dividas['total_quitado'] ?? 0;
                        $dividas_pendentes = $dados_dividas['dividas_pendentes'] ?? 0;
                        $dividas_quitadas = $dados_dividas['dividas_quitadas'] ?? 0;
                        $total_rendas = $dados_rendas['total_renda_mensal'] ?? 0;
                        $quantidade_rendas = $dados_rendas['total_rendas'] ?? 0;
                        
                        echo "Valores processados:<br>";
                        echo "- Total pendente: R$ " . number_format($total_pendente, 2, ',', '.') . "<br>";
                        echo "- Total quitado: R$ " . number_format($total_quitado, 2, ',', '.') . "<br>";
                        echo "- Dívidas pendentes: " . $dividas_pendentes . "<br>";
                        echo "- Dívidas quitadas: " . $dividas_quitadas . "<br>";
                        echo "- Total rendas: R$ " . number_format($total_rendas, 2, ',', '.') . "<br>";
                        echo "- Quantidade rendas: " . $quantidade_rendas . "<br>";
                        
                        // Inserir histórico
                        $total_dividas = $total_pendente + $total_quitado;

                        $query = "INSERT INTO historicos
                            (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
                             dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas)
                            VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                                    :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas)";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->bindParam(':mes_referencia', $mes_referencia);
                        $stmt->bindParam(':total_dividas', $total_dividas);
                        $stmt->bindParam(':total_pendente', $total_pendente);
                        $stmt->bindParam(':total_quitado', $total_quitado);
                        $stmt->bindParam(':dividas_pendentes', $dividas_pendentes);
                        $stmt->bindParam(':dividas_quitadas', $dividas_quitadas);
                        $stmt->bindParam(':total_rendas', $total_rendas);
                        $stmt->bindParam(':quantidade_rendas', $quantidade_rendas);
                        
                        if (!$stmt->execute()) {
                            throw new Exception('Erro ao inserir histórico: ' . implode(', ', $stmt->errorInfo()));
                        }
                        echo "✓ Histórico inserido com sucesso<br>";
                        
                        // Alterar todas as dívidas quitadas para pendente e atualizar data de vencimento
                        $data_vencimento_atual = date('Y-m-d');
                        $query = "UPDATE dividas SET
                            quitada = 0,
                            data_quitacao = NULL,
                            data_vencimento = :data_vencimento
                            WHERE usuario_id = :user_id AND quitada = 1";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->bindParam(':data_vencimento', $data_vencimento_atual);
                        
                        if (!$stmt->execute()) {
                            throw new Exception('Erro ao atualizar dívidas: ' . implode(', ', $stmt->errorInfo()));
                        }
                        echo "✓ Dívidas atualizadas (" . $stmt->rowCount() . " registros afetados)<br>";
                        
                        $db->commit();
                        echo "✓ Transação confirmada<br>";
                        
                        $message = 'Fechamento mensal realizado com sucesso! Histórico salvo e dívidas resetadas.';
                        $message_type = 'success';
                        
                        echo "<h2 style='color: green;'>✅ " . $message . "</h2>";
                        
                    } catch (Exception $e) {
                        try {
                            if ($db->inTransaction()) {
                                $db->rollback();
                                echo "⚠ Transação revertida<br>";
                            }
                        } catch (Exception $rollbackError) {
                            echo "⚠ Erro no rollback: " . $rollbackError->getMessage() . "<br>";
                        }

                        // Log detalhado do erro
                        error_log("Erro no fechamento mensal - User ID: $user_id - Erro: " . $e->getMessage());
                        error_log("Stack trace: " . $e->getTraceAsString());

                        $message = 'Erro ao realizar fechamento: ' . $e->getMessage();
                        $message_type = 'danger';

                        echo "<h2 style='color: red;'>❌ " . $message . "</h2>";
                        echo "<p><strong>Detalhes:</strong> " . $e->getFile() . " linha " . $e->getLine() . "</p>";
                        echo "<p><strong>Stack trace:</strong></p>";
                        echo "<pre>" . $e->getTraceAsString() . "</pre>";
                    }
                    break;
                    
                default:
                    echo "Action não reconhecida: " . $_POST['action'] . "<br>";
                    break;
            }
        } else {
            echo "❌ Action não definida<br>";
        }
    } else {
        echo "❌ Nenhum POST detectado<br>";
    }
    
    // Mostrar resultado final
    if ($message) {
        echo "<div class='alert alert-" . ($message_type == 'success' ? 'success' : ($message_type == 'warning' ? 'warning' : 'danger')) . "'>";
        echo $message;
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO GERAL:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
