# Botão de Remover Histórico - Implementação

## 🎯 **Funcionalidade Implementada**

Adici<PERSON><PERSON> botão "Remover" na coluna "Ações" da página de históricos para permitir a exclusão de fechamentos mensais.

## 🔧 **Alterações Realizadas**

### **Arquivo:** `historicos.php`

#### 1. **Processamento da Ação de Exclusão** (linhas 21-66)
```php
// Processar ações
if ($_POST && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'delete_historico':
            $historico_id = intval($_POST['historico_id']);
            
            if ($historico_id > 0) {
                try {
                    // Verificar se o histórico pertence ao usuário
                    $query = "SELECT id FROM historicos WHERE id = :id AND usuario_id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':id', $historico_id);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();
                    
                    if ($stmt->fetch()) {
                        // Excluir o histórico
                        $query = "DELETE FROM historicos WHERE id = :id AND usuario_id = :user_id";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':id', $historico_id);
                        $stmt->bindParam(':user_id', $user_id);
                        
                        if ($stmt->execute()) {
                            $message = 'Histórico excluído com sucesso!';
                            $message_type = 'success';
                        } else {
                            $message = 'Erro ao excluir histórico.';
                            $message_type = 'danger';
                        }
                    } else {
                        $message = 'Histórico não encontrado ou não pertence ao usuário.';
                        $message_type = 'danger';
                    }
                } catch (Exception $e) {
                    $message = 'Erro ao excluir histórico: ' . $e->getMessage();
                    $message_type = 'danger';
                }
            }
            break;
    }
}
```

#### 2. **Exibição de Mensagens** (linhas 125-131)
```php
<?php if ($message): ?>
<div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>
```

#### 3. **Botão de Remover na Tabela** (linhas 283-290)
```php
<td>
    <button class="btn btn-sm btn-info me-1" onclick="verDetalhes(<?php echo htmlspecialchars(json_encode($historico)); ?>)">
        <i class="fas fa-eye"></i> Ver
    </button>
    <button class="btn btn-sm btn-danger" onclick="confirmarExclusao(<?php echo $historico['id']; ?>, '<?php echo $nome_mes; ?>')">
        <i class="fas fa-trash"></i> Remover
    </button>
</td>
```

#### 4. **JavaScript para Confirmação** (linhas 534-557)
```javascript
// Função para confirmar exclusão de histórico
function confirmarExclusao(historicoId, nomeHistorico) {
    if (confirm('Tem certeza que deseja excluir o histórico de "' + nomeHistorico + '"?\n\nEsta ação não pode ser desfeita.')) {
        // Criar formulário para enviar dados
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete_historico';

        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'historico_id';
        idInput.value = historicoId;

        form.appendChild(actionInput);
        form.appendChild(idInput);
        document.body.appendChild(form);
        form.submit();
    }
}
```

## 🔒 **Recursos de Segurança**

1. **Verificação de Propriedade**: Só permite excluir históricos que pertencem ao usuário logado
2. **Validação de ID**: Verifica se o ID do histórico é válido (número inteiro > 0)
3. **Confirmação Dupla**: JavaScript solicita confirmação antes da exclusão
4. **Tratamento de Erros**: Captura e exibe erros de forma amigável

## 🎨 **Interface do Usuário**

- **Botão Vermelho**: Cor vermelha (btn-danger) para indicar ação destrutiva
- **Ícone de Lixeira**: Font Awesome `fa-trash` para clareza visual
- **Confirmação**: Dialog de confirmação com nome do mês para evitar exclusões acidentais
- **Mensagens**: Alertas Bootstrap para feedback do usuário

## 📋 **Como Funciona**

1. **Usuário clica** no botão "Remover" ao lado de um histórico
2. **JavaScript exibe** confirmação com o nome do mês
3. **Se confirmado**, cria formulário POST com action='delete_historico'
4. **PHP processa** a exclusão verificando permissões
5. **Página recarrega** com mensagem de sucesso/erro
6. **Histórico é removido** da lista e do banco de dados

## ✅ **Funcionalidades**

- ✅ **Botão de remover** visível na coluna "Ações"
- ✅ **Confirmação** antes da exclusão
- ✅ **Segurança** - só remove históricos do usuário
- ✅ **Feedback** - mensagens de sucesso/erro
- ✅ **Interface** - design consistente com o sistema
- ✅ **Responsivo** - funciona em dispositivos móveis

## 🧪 **Para Testar**

1. Acesse: `https://melhorcupom.shop/e1dividas/historicos.php`
2. Localize um histórico na tabela
3. Clique no botão vermelho "Remover"
4. Confirme a exclusão no dialog
5. Verifique a mensagem de sucesso
6. Confirme que o histórico foi removido da lista

A funcionalidade está completa e pronta para uso! 🎉
