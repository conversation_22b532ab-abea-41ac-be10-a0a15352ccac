<?php
// Ativar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug - Históricos</h2>";

try {
    echo "<p>1. Carregando auth.php...</p>";
    require_once 'includes/auth.php';
    echo "<p>✓ Auth carregado</p>";
    
    echo "<p>2. Verificando login...</p>";
    if (!isLoggedIn()) {
        echo "<p>❌ Usuário não logado</p>";
        exit();
    }
    echo "<p>✓ Usuário logado: " . $_SESSION['user_name'] . "</p>";
    
    echo "<p>3. Carregando database.php...</p>";
    require_once 'config/database.php';
    echo "<p>✓ Database carregado</p>";
    
    echo "<p>4. Conectando ao banco...</p>";
    $database = new Database();
    $db = $database->getConnection();
    echo "<p>✓ Conexão estabelecida</p>";
    
    $user_id = $_SESSION['user_id'];
    echo "<p>5. User ID: " . $user_id . "</p>";
    
    echo "<p>6. Verificando tabela historicos...</p>";
    $query = "SHOW TABLES LIKE 'historicos'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "<p>⚠️ Tabela historicos não existe. Criando...</p>";
        
        $createTable = "CREATE TABLE historicos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            mes_referencia VARCHAR(7) NOT NULL,
            total_dividas DECIMAL(10,2) DEFAULT 0,
            total_pendente DECIMAL(10,2) DEFAULT 0,
            total_quitado DECIMAL(10,2) DEFAULT 0,
            dividas_pendentes INT DEFAULT 0,
            dividas_quitadas INT DEFAULT 0,
            total_rendas DECIMAL(10,2) DEFAULT 0,
            quantidade_rendas INT DEFAULT 0,
            data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $db->exec($createTable);
        echo "<p>✓ Tabela historicos criada</p>";
    } else {
        echo "<p>✓ Tabela historicos existe</p>";
    }
    
    echo "<p>7. Buscando históricos...</p>";
    $query = "SELECT * FROM historicos WHERE usuario_id = ? ORDER BY mes_referencia DESC";
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $historicos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>✓ Encontrados " . count($historicos) . " histórico(s)</p>";
    
    if (!empty($historicos)) {
        echo "<h3>Dados dos Históricos:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Mês</th><th>Gastos</th><th>Rendas</th><th>Data</th></tr>";
        
        foreach ($historicos as $h) {
            echo "<tr>";
            echo "<td>" . $h['id'] . "</td>";
            echo "<td>" . $h['mes_referencia'] . "</td>";
            echo "<td>R$ " . number_format($h['total_quitado'], 2, ',', '.') . "</td>";
            echo "<td>R$ " . number_format($h['total_rendas'], 2, ',', '.') . "</td>";
            echo "<td>" . $h['data_fechamento'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p>8. Testando includes...</p>";
    $page_title = 'Debug Históricos';
    
    // Testar se o header funciona
    ob_start();
    include 'includes/header.php';
    $header_content = ob_get_clean();
    
    if (strlen($header_content) > 100) {
        echo "<p>✓ Header carregado com sucesso</p>";
    } else {
        echo "<p>❌ Problema no header</p>";
        echo "<pre>" . htmlspecialchars($header_content) . "</pre>";
    }
    
    echo "<h3>✅ Todos os testes passaram!</h3>";
    echo "<p><a href='historicos_simples.php'>Testar versão simples</a></p>";
    echo "<p><a href='historicos.php'>Testar versão original</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Erro encontrado:</h3>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
