<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception("Erro na conexão com o banco de dados");
    }

    $user_id = $_SESSION['user_id'];
} catch (Exception $e) {
    die("Erro: " . $e->getMessage());
}

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'definir_meta':
                $meta_valor = floatval(str_replace(['.', ','], ['', '.'], str_replace('R$ ', '', $_POST['meta_valor'])));
                
                // Verificar se já existe reserva para o usuário
                $query = "SELECT id FROM reserva_emergencia WHERE usuario_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    // Atualizar meta existente
                    $query = "UPDATE reserva_emergencia SET meta_valor = :meta_valor WHERE usuario_id = :user_id";
                } else {
                    // Criar nova reserva
                    $query = "INSERT INTO reserva_emergencia (usuario_id, meta_valor) VALUES (:user_id, :meta_valor)";
                }
                
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':meta_valor', $meta_valor);
                $stmt->execute();
                
                $success_message = "Meta da reserva de emergência definida com sucesso!";
                break;
                
            case 'adicionar_valor':
                $tipo = $_POST['tipo'];
                $valor = floatval(str_replace(['.', ','], ['', '.'], str_replace('R$ ', '', $_POST['valor'])));
                $descricao = $_POST['descricao'];
                
                // Verificar se existe reserva
                $query = "SELECT id, valor_atual FROM reserva_emergencia WHERE usuario_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                
                if ($stmt->rowCount() == 0) {
                    // Criar reserva se não existir
                    $query = "INSERT INTO reserva_emergencia (usuario_id, valor_atual) VALUES (:user_id, 0)";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();
                    $valor_atual = 0;
                } else {
                    $reserva = $stmt->fetch(PDO::FETCH_ASSOC);
                    $valor_atual = $reserva['valor_atual'];
                }
                
                // Calcular novo valor
                if ($tipo === 'deposito') {
                    $novo_valor = $valor_atual + $valor;
                } else {
                    $novo_valor = max(0, $valor_atual - $valor);
                }
                
                // Atualizar valor da reserva
                $query = "UPDATE reserva_emergencia SET valor_atual = :novo_valor WHERE usuario_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':novo_valor', $novo_valor);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                
                // Registrar movimentação
                $query = "INSERT INTO movimentacoes_reserva (usuario_id, tipo, valor, descricao) VALUES (:user_id, :tipo, :valor, :descricao)";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':tipo', $tipo);
                $stmt->bindParam(':valor', $valor);
                $stmt->bindParam(':descricao', $descricao);
                $stmt->execute();
                
                $success_message = ucfirst($tipo) . " realizado com sucesso!";
                break;
        }
    }
}

// Buscar dados da reserva
try {
    $query = "SELECT * FROM reserva_emergencia WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $reserva = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$reserva) {
        $reserva = [
            'valor_atual' => 0,
            'meta_valor' => 0
        ];
    }
} catch (PDOException $e) {
    // Se a tabela não existir, usar valores padrão
    $reserva = [
        'valor_atual' => 0,
        'meta_valor' => 0
    ];
    $error_message = "Tabelas não encontradas. Execute o script de atualização do banco de dados.";
}

// Buscar movimentações recentes
try {
    $query = "SELECT * FROM movimentacoes_reserva WHERE usuario_id = :user_id ORDER BY data_movimentacao DESC LIMIT 10";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $movimentacoes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calcular estatísticas
    $query = "SELECT
        COUNT(*) as total_movimentacoes,
        SUM(CASE WHEN tipo = 'deposito' THEN valor ELSE 0 END) as total_depositos,
        SUM(CASE WHEN tipo = 'retirada' THEN valor ELSE 0 END) as total_retiradas
    FROM movimentacoes_reserva WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $estatisticas = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Se as tabelas não existirem, usar valores padrão
    $movimentacoes = [];
    $estatisticas = [
        'total_movimentacoes' => 0,
        'total_depositos' => 0,
        'total_retiradas' => 0
    ];
}

$page_title = 'Reserva de Emergência';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-piggy-bank me-2"></i>Reserva de Emergência</h1>
            <div>
                <button class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#depositoModal">
                    <i class="fas fa-plus me-2"></i>Adicionar Valor
                </button>
                <button class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#retiradaModal">
                    <i class="fas fa-minus me-2"></i>Retirar Valor
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#metaModal">
                    <i class="fas fa-target me-2"></i>Definir Meta
                </button>
            </div>
        </div>
    </div>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
    <br><a href="update_database.php" class="btn btn-warning btn-sm mt-2">Atualizar Banco de Dados</a>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Cards de Resumo -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #28a745, #20c997);">
            <div class="card-body">
                <h5><i class="fas fa-piggy-bank me-2"></i>Valor Atual</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($reserva['valor_atual'], 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0">Reserva disponível</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #FE7743, #e5663a);">
            <div class="card-body">
                <h5><i class="fas fa-target me-2"></i>Meta</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($reserva['meta_valor'], 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0">
                    <?php 
                    if ($reserva['meta_valor'] > 0) {
                        $progresso = ($reserva['valor_atual'] / $reserva['meta_valor']) * 100;
                        echo number_format($progresso, 1) . '% atingido';
                    } else {
                        echo 'Meta não definida';
                    }
                    ?>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #447D9B, #6c757d);">
            <div class="card-body">
                <h5><i class="fas fa-exchange-alt me-2"></i>Movimentações</h5>
                <h3><?php echo $estatisticas['total_movimentacoes'] ?: 0; ?></h3>
                <p class="mb-0">Total de operações</p>
            </div>
        </div>
    </div>
</div>

<!-- Progresso da Meta -->
<?php if ($reserva['meta_valor'] > 0): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Progresso da Meta</h5>
            </div>
            <div class="card-body">
                <?php 
                $progresso = ($reserva['valor_atual'] / $reserva['meta_valor']) * 100;
                $progresso = min(100, $progresso);
                ?>
                <div class="progress mb-3" style="height: 25px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $progresso; ?>%">
                        <?php echo number_format($progresso, 1); ?>%
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-md-4">
                        <small class="text-muted">Valor Atual</small>
                        <div class="fw-bold">R$ <?php echo number_format($reserva['valor_atual'], 2, ',', '.'); ?></div>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">Falta para Meta</small>
                        <div class="fw-bold">R$ <?php echo number_format(max(0, $reserva['meta_valor'] - $reserva['valor_atual']), 2, ',', '.'); ?></div>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">Meta</small>
                        <div class="fw-bold">R$ <?php echo number_format($reserva['meta_valor'], 2, ',', '.'); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Histórico de Movimentações -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Últimas Movimentações</h5>
    </div>
    <div class="card-body">
        <?php if (empty($movimentacoes)): ?>
            <div class="text-center py-5">
                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Nenhuma movimentação registrada</h5>
                <p class="text-muted">Comece adicionando um valor à sua reserva de emergência.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Tipo</th>
                            <th>Valor</th>
                            <th>Descrição</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($movimentacoes as $mov): ?>
                        <tr>
                            <td><?php echo date('d/m/Y H:i', strtotime($mov['data_movimentacao'])); ?></td>
                            <td>
                                <?php if ($mov['tipo'] === 'deposito'): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-plus me-1"></i>Depósito
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-minus me-1"></i>Retirada
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="fw-bold <?php echo $mov['tipo'] === 'deposito' ? 'text-success' : 'text-warning'; ?>">
                                <?php echo $mov['tipo'] === 'deposito' ? '+' : '-'; ?>R$ <?php echo number_format($mov['valor'], 2, ',', '.'); ?>
                            </td>
                            <td><?php echo htmlspecialchars($mov['descricao']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal Definir Meta -->
<div class="modal fade" id="metaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-target me-2"></i>Definir Meta da Reserva</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="definir_meta">
                    <div class="mb-3">
                        <label for="meta_valor" class="form-label">Valor da Meta</label>
                        <input type="text" class="form-control money-input" id="meta_valor" name="meta_valor"
                               value="R$ <?php echo number_format($reserva['meta_valor'], 2, ',', '.'); ?>" required>
                        <div class="form-text">Defina o valor que deseja atingir em sua reserva de emergência.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Definir Meta</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Adicionar Depósito -->
<div class="modal fade" id="depositoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Adicionar Valor à Reserva</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="adicionar_valor">
                    <input type="hidden" name="tipo" value="deposito">
                    <div class="mb-3">
                        <label for="valor_deposito" class="form-label">Valor do Depósito</label>
                        <input type="text" class="form-control money-input" id="valor_deposito" name="valor" placeholder="R$ 0,00" required>
                    </div>
                    <div class="mb-3">
                        <label for="descricao_deposito" class="form-label">Descrição</label>
                        <textarea class="form-control" id="descricao_deposito" name="descricao" rows="3"
                                  placeholder="Ex: Sobra do salário, economia mensal..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">Adicionar Valor</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Retirar Valor -->
<div class="modal fade" id="retiradaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-minus me-2"></i>Retirar Valor da Reserva</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="adicionar_valor">
                    <input type="hidden" name="tipo" value="retirada">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Atenção:</strong> Retire valores da reserva apenas em casos de real emergência.
                    </div>
                    <div class="mb-3">
                        <label for="valor_retirada" class="form-label">Valor da Retirada</label>
                        <input type="text" class="form-control money-input" id="valor_retirada" name="valor" placeholder="R$ 0,00" required>
                        <div class="form-text">Valor disponível: R$ <?php echo number_format($reserva['valor_atual'], 2, ',', '.'); ?></div>
                    </div>
                    <div class="mb-3">
                        <label for="descricao_retirada" class="form-label">Motivo da Retirada</label>
                        <textarea class="form-control" id="descricao_retirada" name="descricao" rows="3"
                                  placeholder="Ex: Emergência médica, reparo urgente..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-warning">Retirar Valor</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Formatação de valores monetários
document.addEventListener('DOMContentLoaded', function() {
    const moneyInputs = document.querySelectorAll('.money-input');

    moneyInputs.forEach(function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = (value / 100).toFixed(2) + '';
            value = value.replace(".", ",");
            value = value.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
            e.target.value = 'R$ ' + value;
        });

        input.addEventListener('focus', function(e) {
            if (e.target.value === 'R$ 0,00') {
                e.target.value = 'R$ ';
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
