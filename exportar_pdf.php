<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

$historico_id = intval($_GET['historico_id'] ?? 0);

if ($historico_id <= 0) {
    die('ID do histórico inválido.');
}

try {
    // Buscar dados do histórico
    $query = "SELECT * FROM historicos WHERE id = :id AND usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $historico_id);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $historico = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$historico) {
        die('Histórico não encontrado.');
    }
    
    // Buscar dados do usuário
    $query = "SELECT nome FROM usuarios WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Buscar dívidas atuais (como referência)
    $query = "SELECT titulo, descricao, valor, categoria, data_vencimento 
             FROM dividas 
             WHERE usuario_id = :user_id 
             ORDER BY titulo";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dividas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Formatação da data
    $mes_referencia = $historico['mes_referencia'];
    $mes_formatado = $mes_referencia;
    
    try {
        if (preg_match('/^\d{4}-\d{2}$/', $mes_referencia)) {
            $date = DateTime::createFromFormat('Y-m', $mes_referencia);
            if ($date) {
                $meses = ['', 'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                         'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
                $mes_formatado = $meses[(int)$date->format('n')] . ' de ' . $date->format('Y');
            }
        }
    } catch (Exception $e) {
        // Usar valor padrão
    }
    
    $saldo_mes = floatval($historico['total_rendas']) - floatval($historico['total_quitado']);
    
    // Configurar headers para PDF (simulado com HTML)
    header('Content-Type: text/html; charset=utf-8');
    
} catch (Exception $e) {
    die('Erro ao buscar dados: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Fechamento - <?php echo $mes_formatado; ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .summary-card {
            flex: 1;
            margin: 0 10px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        .summary-card .value {
            font-size: 20px;
            font-weight: bold;
        }
        .danger { color: #dc3545; }
        .success { color: #28a745; }
        .primary { color: #007bff; }
        
        .dividas-section {
            margin-top: 30px;
        }
        .dividas-section h2 {
            color: #007bff;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .categoria {
            background-color: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Relatório de Fechamento Mensal</h1>
        <p><strong><?php echo $mes_formatado; ?></strong></p>
        <p>Usuário: <?php echo htmlspecialchars($usuario['nome'] ?? 'N/A'); ?></p>
        <p>Gerado em: <?php echo date('d/m/Y H:i:s'); ?></p>
        <p>Data do Fechamento: <?php echo date('d/m/Y H:i', strtotime($historico['data_fechamento'])); ?></p>
    </div>
    
    <div class="summary">
        <div class="summary-card">
            <h3>Dívidas</h3>
            <div class="value danger">
                <?php echo $historico['dividas_pendentes'] + $historico['dividas_quitadas']; ?> total
            </div>
            <p><?php echo $historico['dividas_quitadas']; ?> quitadas | <?php echo $historico['dividas_pendentes']; ?> pendentes</p>
            <p><strong>Valor Quitado:</strong><br>R$ <?php echo number_format($historico['total_quitado'], 2, ',', '.'); ?></p>
        </div>

        <div class="summary-card">
            <h3>Rendas</h3>
            <div class="value success">
                <?php echo $historico['quantidade_rendas']; ?> fontes
            </div>
            <p><strong>Total de Rendas:</strong><br>R$ <?php echo number_format($historico['total_rendas'], 2, ',', '.'); ?></p>
        </div>

        <div class="summary-card">
            <h3>Reserva de Emergência</h3>
            <div class="value primary" style="color: #ff8c00;">
                R$ <?php echo number_format($historico['reserva_emergencia'] ?? 0, 2, ',', '.'); ?>
            </div>
            <p>Valor guardado para emergências</p>
        </div>

        <div class="summary-card">
            <h3>Saldo do Mês</h3>
            <div class="value <?php echo $saldo_mes >= 0 ? 'success' : 'danger'; ?>">
                R$ <?php echo number_format(abs($saldo_mes), 2, ',', '.'); ?>
            </div>
            <p><?php echo $saldo_mes >= 0 ? 'Economia' : 'Déficit'; ?></p>
        </div>
    </div>
    
    <div class="dividas-section">
        <h2>Dívidas do Fechamento</h2>
        
        <?php if (!empty($dividas)): ?>
        <table>
            <thead>
                <tr>
                    <th>Título</th>
                    <th>Descrição</th>
                    <th>Categoria</th>
                    <th>Valor</th>
                    <th>Vencimento</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($dividas as $divida): ?>
                <tr>
                    <td><strong><?php echo htmlspecialchars($divida['titulo']); ?></strong></td>
                    <td><?php echo htmlspecialchars($divida['descricao'] ?: '-'); ?></td>
                    <td>
                        <?php if ($divida['categoria']): ?>
                            <span class="categoria"><?php echo htmlspecialchars($divida['categoria']); ?></span>
                        <?php else: ?>
                            <span class="categoria">Sem categoria</span>
                        <?php endif; ?>
                    </td>
                    <td><strong>R$ <?php echo number_format($divida['valor'], 2, ',', '.'); ?></strong></td>
                    <td>
                        <?php 
                        if ($divida['data_vencimento']) {
                            echo date('d/m/Y', strtotime($divida['data_vencimento']));
                        } else {
                            echo 'Não definido';
                        }
                        ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <p>Nenhuma dívida encontrada para este fechamento.</p>
        <?php endif; ?>
    </div>
    
    <div class="footer">
        <p>Relatório gerado pelo Sistema E1Dívidas</p>
        <p>Este documento contém informações confidenciais do usuário</p>
    </div>
    
    <div class="no-print" style="margin-top: 30px; text-align: center;">
        <button onclick="window.print()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            Imprimir / Salvar como PDF
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Fechar
        </button>
    </div>
</body>
</html>
