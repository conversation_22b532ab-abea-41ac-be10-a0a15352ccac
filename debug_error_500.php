<?php
// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>Debug Erro 500 - E1Dividas</h1>";

// Teste 1: Verificar se o PHP está funcionando
echo "<h2>1. Teste PHP Básico</h2>";
echo "✓ PHP versão: " . phpversion() . "<br>";
echo "✓ Extensões carregadas: " . implode(', ', get_loaded_extensions()) . "<br>";

// Teste 2: Verificar se os arquivos existem
echo "<h2>2. Verificando arquivos</h2>";
$files_to_check = [
    'config/database.php',
    'includes/auth.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ Arquivo $file existe<br>";
    } else {
        echo "✗ Arquivo $file NÃO existe<br>";
    }
}

// Teste 3: Verificar sintaxe dos arquivos
echo "<h2>3. Verificando sintaxe dos arquivos</h2>";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_var = 0;
        exec("php -l $file 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "✓ Sintaxe de $file está OK<br>";
        } else {
            echo "✗ Erro de sintaxe em $file:<br>";
            foreach ($output as $line) {
                echo "&nbsp;&nbsp;$line<br>";
            }
        }
    }
}

// Teste 4: Testar includes
echo "<h2>4. Testando includes</h2>";
try {
    require_once 'config/database.php';
    echo "✓ config/database.php incluído com sucesso<br>";
} catch (Exception $e) {
    echo "✗ Erro ao incluir config/database.php: " . $e->getMessage() . "<br>";
}

try {
    require_once 'includes/auth.php';
    echo "✓ includes/auth.php incluído com sucesso<br>";
} catch (Exception $e) {
    echo "✗ Erro ao incluir includes/auth.php: " . $e->getMessage() . "<br>";
}

// Teste 5: Testar conexão com banco
echo "<h2>5. Testando conexão com banco</h2>";
try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "✓ Conexão com banco OK<br>";
        
        // Testar query simples
        $stmt = $db->query("SELECT 1 as test");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Query de teste OK: " . $result['test'] . "<br>";
    } else {
        echo "✗ Falha na conexão com banco<br>";
    }
} catch (Exception $e) {
    echo "✗ Erro na conexão com banco: " . $e->getMessage() . "<br>";
}

// Teste 6: Verificar sessão
echo "<h2>6. Verificando sessão</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
    echo "✓ Sessão iniciada<br>";
} else {
    echo "✓ Sessão já estava ativa<br>";
}

if (isset($_SESSION['user_id'])) {
    echo "✓ Usuário logado - ID: " . $_SESSION['user_id'] . "<br>";
} else {
    echo "⚠ Usuário não está logado<br>";
}

// Teste 7: Simular o que acontece no dividas.php
echo "<h2>7. Simulando dividas.php</h2>";
try {
    // Simular requireLogin()
    if (!isset($_SESSION['user_id'])) {
        echo "⚠ Usuário não logado - seria redirecionado para login.php<br>";
    } else {
        echo "✓ Usuário logado - pode acessar dividas.php<br>";
        
        // Testar query das dívidas
        $user_id = $_SESSION['user_id'];
        $query = "SELECT COUNT(*) as total FROM dividas WHERE usuario_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Query de dívidas OK - Total: " . $result['total'] . "<br>";
    }
} catch (Exception $e) {
    echo "✗ Erro na simulação: " . $e->getMessage() . "<br>";
}

// Teste 8: Verificar permissões de arquivo
echo "<h2>8. Verificando permissões</h2>";
$files_to_check_perms = [
    'dividas.php',
    'config/database.php',
    'includes/auth.php'
];

foreach ($files_to_check_perms as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_octal = substr(sprintf('%o', $perms), -4);
        echo "✓ Permissões de $file: $perms_octal<br>";
    }
}

echo "<h2 style='color: green;'>✓ Debug concluído!</h2>";
echo "<p>Se você ainda está vendo erro 500, verifique os logs do servidor web.</p>";
?>
