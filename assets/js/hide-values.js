/**
 * Sistema de Hide/Show Values para E1Dividas
 * Permite ocultar/mostrar valores financeiros por questões de segurança
 */

// Função para alternar visibilidade dos valores
function toggleValuesVisibility() {
    console.log('toggleValuesVisibility() chamada');

    const valueDisplays = document.querySelectorAll('.value-display');
    const valueHiddens = document.querySelectorAll('.value-hidden');
    const toggleIcon = document.getElementById('toggleIcon');
    const toggleText = document.getElementById('toggleText');

    console.log('Elementos encontrados:', {
        valueDisplays: valueDisplays.length,
        valueHiddens: valueHiddens.length,
        toggleIcon: !!toggleIcon,
        toggleText: !!toggleText
    });

    // Verificar se existem elementos na página
    if (valueDisplays.length === 0) {
        console.warn('Nenhum elemento .value-display encontrado');
        alert('Nenhum valor encontrado para ocultar/mostrar');
        return;
    }

    // Verificar estado atual
    const isHidden = valueDisplays[0].style.display === 'none';
    console.log('Estado atual - isHidden:', isHidden);
    
    if (isHidden) {
        // Mostrar valores
        valueDisplays.forEach(el => el.style.display = 'inline');
        valueHiddens.forEach(el => el.style.display = 'none');
        
        if (toggleIcon) toggleIcon.className = 'fas fa-eye';
        if (toggleText) toggleText.textContent = 'Ocultar Valores';
        
        document.body.classList.remove('values-hidden');
        localStorage.setItem('valuesHidden', 'false');
        
        // Disparar evento personalizado
        document.dispatchEvent(new CustomEvent('valuesShown'));
    } else {
        // Ocultar valores
        valueDisplays.forEach(el => el.style.display = 'none');
        valueHiddens.forEach(el => el.style.display = 'inline');
        
        if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
        if (toggleText) toggleText.textContent = 'Mostrar Valores';
        
        document.body.classList.add('values-hidden');
        localStorage.setItem('valuesHidden', 'true');
        
        // Disparar evento personalizado
        document.dispatchEvent(new CustomEvent('valuesHidden'));
    }
}

// Função para aplicar estado salvo
function applyStoredVisibilityState() {
    const valuesHidden = localStorage.getItem('valuesHidden') === 'true';
    
    if (valuesHidden) {
        const valueDisplays = document.querySelectorAll('.value-display');
        const valueHiddens = document.querySelectorAll('.value-hidden');
        const toggleIcon = document.getElementById('toggleIcon');
        const toggleText = document.getElementById('toggleText');
        
        if (valueDisplays.length > 0) {
            valueDisplays.forEach(el => el.style.display = 'none');
            valueHiddens.forEach(el => el.style.display = 'inline');
            
            if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
            if (toggleText) toggleText.textContent = 'Mostrar Valores';
            
            document.body.classList.add('values-hidden');
        }
    }
}

// Função para inicializar o sistema
function initHideValuesSystem() {
    console.log('Inicializando sistema Hide/Show Values...');

    // Aplicar estado salvo
    applyStoredVisibilityState();

    // Adicionar listener para o botão toggle se existir
    const toggleButton = document.getElementById('toggleValues');
    if (toggleButton) {
        console.log('Botão toggle encontrado, adicionando listener...');
        // Remover onclick inline se existir para evitar conflitos
        toggleButton.removeAttribute('onclick');
        toggleButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Botão clicado!');
            toggleValuesVisibility();
        });
    } else {
        console.warn('Botão toggle não encontrado!');
    }

    // Adicionar atalho de teclado (Ctrl + H)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'h') {
            e.preventDefault();
            console.log('Atalho Ctrl+H pressionado!');
            toggleValuesVisibility();
        }
    });
}

// Inicializar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM carregado, inicializando sistema...');
    initHideValuesSystem();
});

// Também tentar inicializar imediatamente caso o DOM já esteja pronto
if (document.readyState !== 'loading') {
    console.log('DOM já carregado, inicializando sistema...');
    initHideValuesSystem();
}

// Exportar funções para uso global
window.toggleValuesVisibility = toggleValuesVisibility;
window.applyStoredVisibilityState = applyStoredVisibilityState;
