<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

// Buscar estatísticas das dívidas
$query = "SELECT 
    COUNT(*) as total_dividas,
    SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END) as total_pendente,
    SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END) as total_quitado,
    COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
    COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas
FROM dividas WHERE usuario_id = :user_id";

$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$stats_dividas = $stmt->fetch(PDO::FETCH_ASSOC);

// Buscar estatísticas das rendas
$query = "SELECT * FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$rendas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calcular renda mensal total considerando frequências
$total_renda_mensal = 0;
$total_rendas = count($rendas);

foreach ($rendas as $renda) {
    $valor_mensal = 0;

    switch ($renda['frequencia']) {
        case 'mensal':
            $valor_mensal = $renda['valor'];
            break;
        case 'semanal':
            $valor_mensal = $renda['valor'] * 4.33; // média de semanas por mês
            break;
        case 'anual':
            $valor_mensal = $renda['valor'] / 12;
            break;
        case 'unico':
            $valor_mensal = 0; // não conta para renda mensal recorrente
            break;
    }

    $total_renda_mensal += $valor_mensal;
}

// Criar array compatível com o código existente
$stats_rendas = [
    'total_rendas' => $total_rendas,
    'total_renda_mensal' => $total_renda_mensal
];

// Buscar dados da reserva de emergência
try {
    $query = "SELECT valor_atual, meta_valor FROM reserva_emergencia WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $reserva = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$reserva) {
        $reserva = [
            'valor_atual' => 0,
            'meta_valor' => 0
        ];
    }
} catch (PDOException $e) {
    // Se a tabela não existir, usar valores padrão
    $reserva = [
        'valor_atual' => 0,
        'meta_valor' => 0
    ];
}

// Buscar dívidas por categoria para gráfico
$query = "SELECT 
    COALESCE(categoria, 'Sem categoria') as categoria,
    COUNT(*) as quantidade,
    SUM(valor) as total
FROM dividas 
WHERE usuario_id = :user_id 
GROUP BY categoria
ORDER BY total DESC";

$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$dividas_por_categoria = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Buscar últimas dívidas
$query = "SELECT titulo, valor, data_vencimento, quitada 
FROM dividas 
WHERE usuario_id = :user_id 
ORDER BY data_criacao DESC 
LIMIT 5";

$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$ultimas_dividas = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Dashboard';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            <small class="text-muted">Visão geral das suas finanças</small>
        </h1>
    </div>
</div>

<!-- Botão Hide/Show Values -->
<div class="row mb-3">
    <div class="col-12 text-end">
        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleValues">
            <i class="fas fa-eye" id="toggleIcon"></i>
            <span id="toggleText">Ocultar Valores</span>
        </button>
    </div>
</div>

<!-- Cards de Estatísticas -->
<div class="row mb-4">
    <div class="col-md-2-4">
        <div class="stats-card">
            <h3>
                <span class="value-display">R$ <?php echo number_format($stats_dividas['total_pendente'] ?? 0, 2, ',', '.'); ?></span>
                <span class="value-hidden" style="display: none;">R$ •••,••</span>
            </h3>
            <p><i class="fas fa-exclamation-triangle me-2"></i>Dívidas Pendentes</p>
        </div>
    </div>

    <div class="col-md-2-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
            <h3>
                <span class="value-display">R$ <?php echo number_format($stats_dividas['total_quitado'] ?? 0, 2, ',', '.'); ?></span>
                <span class="value-hidden" style="display: none;">R$ •••,••</span>
            </h3>
            <p><i class="fas fa-check-circle me-2"></i>Dívidas Quitadas</p>
        </div>
    </div>

    <div class="col-md-2-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #447D9B, #6c757d);">
            <h3>
                <span class="value-display">R$ <?php echo number_format($stats_rendas['total_renda_mensal'] ?? 0, 2, ',', '.'); ?></span>
                <span class="value-hidden" style="display: none;">R$ •••,••</span>
            </h3>
            <p><i class="fas fa-money-bill-wave me-2"></i>Renda Mensal</p>
        </div>
    </div>

    <div class="col-md-2-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #6f42c1, #e83e8c);">
            <h3>
                <span class="value-display">R$ <?php echo number_format($reserva['valor_atual'] ?? 0, 2, ',', '.'); ?></span>
                <span class="value-hidden" style="display: none;">R$ •••,••</span>
            </h3>
            <p><i class="fas fa-piggy-bank me-2"></i>Reserva de Emergência</p>
        </div>
    </div>

    <div class="col-md-2-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #273F4F, #495057);">
            <h3><?php echo $stats_dividas['total_dividas'] ?? 0; ?></h3>
            <p><i class="fas fa-list me-2"></i>Total de Dívidas</p>
        </div>
    </div>
</div>

<div class="row">
    <!-- Gráfico de Dívidas por Categoria -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Dívidas por Categoria</h5>
            </div>
            <div class="card-body">
                <div style="height: 250px; position: relative;">
                    <canvas id="categoriasChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráfico de Status das Dívidas -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-doughnut me-2"></i>Status das Dívidas</h5>
            </div>
            <div class="card-body">
                <div style="height: 250px; position: relative;">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Últimas Dívidas -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Últimas Dívidas Cadastradas</h5>
            </div>
            <div class="card-body">
                <?php if (empty($ultimas_dividas)): ?>
                    <p class="text-muted text-center py-4">
                        <i class="fas fa-info-circle me-2"></i>
                        Nenhuma dívida cadastrada ainda.
                        <a href="dividas.php" class="btn btn-primary btn-sm ms-2">
                            <i class="fas fa-plus me-1"></i>Cadastrar Primeira Dívida
                        </a>
                    </p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Título</th>
                                    <th>Valor</th>
                                    <th>Vencimento</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ultimas_dividas as $divida): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($divida['titulo']); ?></td>
                                    <td>R$ <?php echo number_format($divida['valor'], 2, ',', '.'); ?></td>
                                    <td>
                                        <?php 
                                        if ($divida['data_vencimento']) {
                                            echo date('d/m/Y', strtotime($divida['data_vencimento']));
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php if ($divida['quitada']): ?>
                                            <span class="badge badge-quitada">Quitada</span>
                                        <?php else: ?>
                                            <span class="badge badge-pendente">Pendente</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Resumo Financeiro -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Resumo Financeiro</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Renda Total:</strong><br>
                    <span class="text-success fs-5">
                        <span class="value-display">R$ <?php echo number_format($stats_rendas['total_renda_mensal'] ?? 0, 2, ',', '.'); ?></span>
                        <span class="value-hidden" style="display: none;">R$ •••,••</span>
                    </span>
                </div>

                <div class="mb-3">
                    <strong>Dívidas Quitadas:</strong><br>
                    <span class="text-info fs-5">
                        <span class="value-display">R$ <?php echo number_format($stats_dividas['total_quitado'] ?? 0, 2, ',', '.'); ?></span>
                        <span class="value-hidden" style="display: none;">R$ •••,••</span>
                    </span>
                </div>

                <div class="mb-3">
                    <strong>Dívidas Pendentes:</strong><br>
                    <span class="text-warning fs-5">
                        <span class="value-display">R$ <?php echo number_format($stats_dividas['total_pendente'] ?? 0, 2, ',', '.'); ?></span>
                        <span class="value-hidden" style="display: none;">R$ •••,••</span>
                    </span>
                </div>

                <div class="mb-3">
                    <strong>Reserva de Emergência:</strong><br>
                    <span class="text-info fs-5">
                        <span class="value-display">R$ <?php echo number_format($reserva['valor_atual'] ?? 0, 2, ',', '.'); ?></span>
                        <span class="value-hidden" style="display: none;">R$ •••,••</span>
                    </span>
                    <?php if ($reserva['meta_valor'] > 0): ?>
                        <br><small class="text-muted">
                            Meta: R$ <?php echo number_format($reserva['meta_valor'], 2, ',', '.'); ?>
                            (<?php echo number_format(($reserva['valor_atual'] / $reserva['meta_valor']) * 100, 1); ?>%)
                        </small>
                    <?php endif; ?>
                </div>

                <hr>

                <div class="mb-3">
                    <strong>Saldo do Mês:</strong><br>
                    <small class="text-muted">Sobra (Rendas - Gastos)</small><br>
                    <?php
                    $saldo = ($stats_rendas['total_renda_mensal'] ?? 0) - ($stats_dividas['total_quitado'] ?? 0);
                    $classe_saldo = $saldo >= 0 ? 'text-success' : 'text-danger';
                    $texto_saldo = $saldo >= 0 ? 'Economia' : 'Déficit';
                    ?>
                    <span class="<?php echo $classe_saldo; ?> fs-5">
                        <span class="value-display">R$ <?php echo number_format(abs($saldo), 2, ',', '.'); ?></span>
                        <span class="value-hidden" style="display: none;">R$ •••,••</span>
                    </span><br>
                    <small class="<?php echo $classe_saldo; ?>"><?php echo $texto_saldo; ?></small>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="dividas.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Nova Dívida
                    </a>
                    <a href="rendas.php" class="btn btn-secondary btn-sm">
                        <i class="fas fa-plus me-1"></i>Nova Renda
                    </a>
                    <a href="reserva-emergencia.php" class="btn btn-success btn-sm">
                        <i class="fas fa-piggy-bank me-1"></i>Reserva de Emergência
                    </a>
                    <a href="historicos.php" class="btn btn-info btn-sm">
                        <i class="fas fa-history me-1"></i>Ver Históricos
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Gráfico de Dívidas por Categoria
const categoriasData = <?php echo json_encode($dividas_por_categoria); ?>;
const categoriasLabels = categoriasData.map(item => item.categoria);
const categoriasValues = categoriasData.map(item => parseFloat(item.total));

const categoriasChart = new Chart(document.getElementById('categoriasChart'), {
    type: 'pie',
    data: {
        labels: categoriasLabels,
        datasets: [{
            data: categoriasValues,
            backgroundColor: [
                '#FE7743',
                '#447D9B',
                '#273F4F',
                '#D7D7D7',
                '#28a745',
                '#dc3545',
                '#ffc107'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Gráfico de Status das Dívidas
const statusChart = new Chart(document.getElementById('statusChart'), {
    type: 'doughnut',
    data: {
        labels: ['Pendentes', 'Quitadas'],
        datasets: [{
            data: [
                <?php echo $stats_dividas['dividas_pendentes'] ?? 0; ?>,
                <?php echo $stats_dividas['dividas_quitadas'] ?? 0; ?>
            ],
            backgroundColor: ['#FE7743', '#28a745']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Sistema de Hide/Show Values - Versão Simplificada
function toggleValuesVisibility() {
    console.log('toggleValuesVisibility chamada');

    const valueDisplays = document.querySelectorAll('.value-display');
    const valueHiddens = document.querySelectorAll('.value-hidden');
    const toggleIcon = document.getElementById('toggleIcon');
    const toggleText = document.getElementById('toggleText');

    console.log('Elementos:', valueDisplays.length, valueHiddens.length);

    if (valueDisplays.length === 0) {
        alert('Nenhum valor encontrado para ocultar/mostrar');
        return;
    }

    const isHidden = valueDisplays[0].style.display === 'none';
    console.log('Estado atual:', isHidden ? 'oculto' : 'visível');

    if (isHidden) {
        // Mostrar valores
        valueDisplays.forEach(el => el.style.display = 'inline');
        valueHiddens.forEach(el => el.style.display = 'none');
        if (toggleIcon) toggleIcon.className = 'fas fa-eye';
        if (toggleText) toggleText.textContent = 'Ocultar Valores';
        localStorage.setItem('valuesHidden', 'false');
        console.log('Valores mostrados');
    } else {
        // Ocultar valores
        valueDisplays.forEach(el => el.style.display = 'none');
        valueHiddens.forEach(el => el.style.display = 'inline');
        if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
        if (toggleText) toggleText.textContent = 'Mostrar Valores';
        localStorage.setItem('valuesHidden', 'true');
        console.log('Valores ocultos');
    }
}

// Inicializar quando DOM carregar
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM carregado');

    // Aplicar estado salvo
    const valuesHidden = localStorage.getItem('valuesHidden') === 'true';
    if (valuesHidden) {
        const valueDisplays = document.querySelectorAll('.value-display');
        const valueHiddens = document.querySelectorAll('.value-hidden');
        const toggleIcon = document.getElementById('toggleIcon');
        const toggleText = document.getElementById('toggleText');

        if (valueDisplays.length > 0) {
            valueDisplays.forEach(el => el.style.display = 'none');
            valueHiddens.forEach(el => el.style.display = 'inline');
            if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
            if (toggleText) toggleText.textContent = 'Mostrar Valores';
        }
    }

    // Adicionar listener ao botão
    const toggleButton = document.getElementById('toggleValues');
    if (toggleButton) {
        console.log('Botão encontrado, adicionando listener');
        toggleButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Botão clicado!');
            toggleValuesVisibility();
        });
    } else {
        console.error('Botão não encontrado!');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
