<?php
/**
 * Instalação Manual do E1Dividas
 * Use este arquivo se o install.php não funcionar
 */

// Configurações do banco - ALTERE AQUI
$host = 'localhost';
$dbname = 'u880879026_e1dividas'; // Seu banco de dados
$username = 'u880879026_e1dividas'; // Seu usuário
$password = 'SUA_SENHA_AQUI'; // Sua senha

// Dados do administrador - ALTERE AQUI
$admin_nome = 'Administrador';
$admin_email = '<EMAIL>';
$admin_senha = 'admin123'; // Altere esta senha!

try {
    // Conectar ao banco
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Instalando E1Dividas...</h2>";
    
    // Criar tabela de usuários
    echo "Criando tabela usuarios...<br>";
    $pdo->exec("CREATE TABLE IF NOT EXISTS usuarios (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        senha VARCHAR(255) NOT NULL,
        data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        ativo BOOLEAN DEFAULT TRUE
    )");
    
    // Criar tabela de dívidas
    echo "Criando tabela dividas...<br>";
    $pdo->exec("CREATE TABLE IF NOT EXISTS dividas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        titulo VARCHAR(100) NOT NULL,
        descricao TEXT,
        valor DECIMAL(10,2) NOT NULL,
        data_vencimento DATE,
        data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        quitada BOOLEAN DEFAULT FALSE,
        data_quitacao TIMESTAMP NULL,
        categoria VARCHAR(50),
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
    )");
    
    // Criar tabela de rendas
    echo "Criando tabela rendas...<br>";
    $pdo->exec("CREATE TABLE IF NOT EXISTS rendas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        titulo VARCHAR(100) NOT NULL,
        descricao TEXT,
        valor DECIMAL(10,2) NOT NULL,
        tipo ENUM('fixo', 'variavel') DEFAULT 'fixo',
        frequencia ENUM('mensal', 'semanal', 'anual', 'unico') DEFAULT 'mensal',
        data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        ativo BOOLEAN DEFAULT TRUE,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
    )");
    
    // Criar índices
    echo "Criando índices...<br>";
    try {
        $pdo->exec("CREATE INDEX idx_dividas_usuario ON dividas(usuario_id)");
    } catch (Exception $e) {
        // Índice pode já existir
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_dividas_quitada ON dividas(quitada)");
    } catch (Exception $e) {
        // Índice pode já existir
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_rendas_usuario ON rendas(usuario_id)");
    } catch (Exception $e) {
        // Índice pode já existir
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_rendas_ativo ON rendas(ativo)");
    } catch (Exception $e) {
        // Índice pode já existir
    }
    
    // Criar usuário administrador
    echo "Criando usuário administrador...<br>";
    $admin_senha_hash = password_hash($admin_senha, PASSWORD_DEFAULT);
    
    // Verificar se já existe
    $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE email = ?");
    $stmt->execute([$admin_email]);
    
    if ($stmt->rowCount() == 0) {
        $stmt = $pdo->prepare("INSERT INTO usuarios (nome, email, senha) VALUES (?, ?, ?)");
        $stmt->execute([$admin_nome, $admin_email, $admin_senha_hash]);
        echo "Usuário administrador criado!<br>";
    } else {
        echo "Usuário administrador já existe!<br>";
    }
    
    // Atualizar arquivo de configuração
    echo "Atualizando configuração...<br>";
    $config_content = "<?php
/**
 * Configuração do Banco de Dados - E1Dividas
 */

class Database {
    private \$host = '$host';
    private \$db_name = '$dbname';
    private \$username = '$username';
    private \$password = '$password';
    private \$conn;

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$this->conn = new PDO(
                \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name,
                \$this->username,
                \$this->password
            );
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            \$this->conn->exec(\"set names utf8\");
        } catch(PDOException \$exception) {
            echo \"Erro de conexão: \" . \$exception->getMessage();
        }
        
        return \$this->conn;
    }
}
?>";
    
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    file_put_contents('config/database.php', $config_content);
    
    // Criar arquivo de lock
    file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
    
    echo "<h3 style='color: green;'>✅ Instalação concluída com sucesso!</h3>";
    echo "<p><strong>Dados de acesso:</strong></p>";
    echo "<p>Email: $admin_email<br>Senha: $admin_senha</p>";
    echo "<p><a href='login.php'>Fazer Login</a></p>";
    echo "<p><strong>IMPORTANTE:</strong> Altere a senha do administrador após o primeiro login!</p>";
    echo "<p><strong>SEGURANÇA:</strong> Remova este arquivo (install_manual.php) após a instalação!</p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro na instalação:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>Verifique:</strong></p>";
    echo "<ul>";
    echo "<li>Se as credenciais do banco estão corretas</li>";
    echo "<li>Se o banco de dados existe</li>";
    echo "<li>Se o usuário tem permissões para criar tabelas</li>";
    echo "</ul>";
}
?>
