<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debug da Transação - Fechamento Mensal</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Usuário logado: ID " . $user_id . "<br>";
    
    $mes_referencia = date('Y-m');
    echo "✓ Mês de referência: " . $mes_referencia . "<br>";
    
    // Testar se a conexão suporta transações
    echo "<h3>1. Testando suporte a transações</h3>";
    echo "Driver: " . $db->getAttribute(PDO::ATTR_DRIVER_NAME) . "<br>";
    echo "Autocommit: " . ($db->getAttribute(PDO::ATTR_AUTOCOMMIT) ? 'ON' : 'OFF') . "<br>";
    
    // Testar transação simples
    echo "<h3>2. Testando transação simples</h3>";
    try {
        $db->beginTransaction();
        echo "✓ beginTransaction() OK<br>";
        echo "inTransaction(): " . ($db->inTransaction() ? 'true' : 'false') . "<br>";
        
        $db->rollback();
        echo "✓ rollback() OK<br>";
        echo "inTransaction() após rollback: " . ($db->inTransaction() ? 'true' : 'false') . "<br>";
    } catch (Exception $e) {
        echo "❌ Erro na transação simples: " . $e->getMessage() . "<br>";
    }
    
    // Verificar se tabela historicos existe
    echo "<h3>3. Verificando tabela historicos</h3>";
    $query = "SHOW TABLES LIKE 'historicos'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$result) {
        echo "⚠ Tabela historicos não existe - criando...<br>";
        
        $create_sql = "CREATE TABLE IF NOT EXISTS historicos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            mes_referencia VARCHAR(7) NOT NULL,
            total_dividas DECIMAL(10,2) DEFAULT 0,
            total_pendente DECIMAL(10,2) DEFAULT 0,
            total_quitado DECIMAL(10,2) DEFAULT 0,
            dividas_pendentes INT DEFAULT 0,
            dividas_quitadas INT DEFAULT 0,
            total_rendas DECIMAL(10,2) DEFAULT 0,
            quantidade_rendas INT DEFAULT 0,
            data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $db->exec($create_sql);
        echo "✓ Tabela historicos criada<br>";
    } else {
        echo "✓ Tabela historicos existe<br>";
    }
    
    // Testar verificação de fechamento existente
    echo "<h3>4. Testando verificação de fechamento existente</h3>";
    $query = "SELECT COUNT(*) as total FROM historicos WHERE usuario_id = :user_id AND mes_referencia = :mes_referencia";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':mes_referencia', $mes_referencia);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $existe_fechamento = $result['total'] > 0;
    
    echo "Fechamento existente: " . ($existe_fechamento ? 'Sim' : 'Não') . "<br>";
    
    if ($existe_fechamento) {
        echo "⚠ Já existe fechamento para este mês. Usando mês de teste...<br>";
        $mes_referencia = date('Y-m') . '-TEST-' . time();
        echo "Novo mês de teste: " . $mes_referencia . "<br>";
    }
    
    // Testar coleta de dados das dívidas
    echo "<h3>5. Testando coleta de dados das dívidas</h3>";
    $query = "SELECT
        COUNT(*) as total_dividas,
        COALESCE(SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END), 0) as total_pendente,
        COALESCE(SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END), 0) as total_quitado,
        COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
        COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas
    FROM dividas WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dados_dividas = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Dados das dívidas coletados:<br>";
    echo "- Total dívidas: " . $dados_dividas['total_dividas'] . "<br>";
    echo "- Total pendente: R$ " . number_format($dados_dividas['total_pendente'], 2, ',', '.') . "<br>";
    echo "- Total quitado: R$ " . number_format($dados_dividas['total_quitado'], 2, ',', '.') . "<br>";
    echo "- Dívidas pendentes: " . $dados_dividas['dividas_pendentes'] . "<br>";
    echo "- Dívidas quitadas: " . $dados_dividas['dividas_quitadas'] . "<br>";
    
    // Testar coleta de dados das rendas
    echo "<h3>6. Testando coleta de dados das rendas</h3>";
    $query = "SELECT
        COUNT(*) as total_rendas,
        COALESCE(SUM(valor), 0) as total_renda_mensal
    FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dados_rendas = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Dados das rendas coletados:<br>";
    echo "- Quantidade rendas: " . $dados_rendas['total_rendas'] . "<br>";
    echo "- Total rendas: R$ " . number_format($dados_rendas['total_renda_mensal'], 2, ',', '.') . "<br>";
    
    // Testar inserção com transação
    echo "<h3>7. Testando inserção com transação completa</h3>";
    
    $db->beginTransaction();
    echo "✓ Transação iniciada<br>";
    echo "inTransaction(): " . ($db->inTransaction() ? 'true' : 'false') . "<br>";
    
    // Preparar dados
    $total_pendente = $dados_dividas['total_pendente'] ?? 0;
    $total_quitado = $dados_dividas['total_quitado'] ?? 0;
    $dividas_pendentes = $dados_dividas['dividas_pendentes'] ?? 0;
    $dividas_quitadas = $dados_dividas['dividas_quitadas'] ?? 0;
    $total_rendas = $dados_rendas['total_renda_mensal'] ?? 0;
    $quantidade_rendas = $dados_rendas['total_rendas'] ?? 0;
    $total_dividas = $total_pendente + $total_quitado;
    
    // Inserir histórico
    $query = "INSERT INTO historicos
        (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
         dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas)
        VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':mes_referencia', $mes_referencia);
    $stmt->bindParam(':total_dividas', $total_dividas);
    $stmt->bindParam(':total_pendente', $total_pendente);
    $stmt->bindParam(':total_quitado', $total_quitado);
    $stmt->bindParam(':dividas_pendentes', $dividas_pendentes);
    $stmt->bindParam(':dividas_quitadas', $dividas_quitadas);
    $stmt->bindParam(':total_rendas', $total_rendas);
    $stmt->bindParam(':quantidade_rendas', $quantidade_rendas);
    
    if ($stmt->execute()) {
        echo "✓ Histórico inserido com sucesso<br>";
        
        $db->commit();
        echo "✓ Transação confirmada<br>";
        echo "inTransaction() após commit: " . ($db->inTransaction() ? 'true' : 'false') . "<br>";
        
        // Limpar teste
        $query = "DELETE FROM historicos WHERE mes_referencia = :mes_referencia";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':mes_referencia', $mes_referencia);
        $stmt->execute();
        echo "✓ Registro de teste removido<br>";
        
        echo "<h2 style='color: green;'>✅ TODOS OS TESTES PASSARAM!</h2>";
        echo "<p>A transação está funcionando corretamente.</p>";
        
    } else {
        echo "❌ Erro na inserção: " . implode(', ', $stmt->errorInfo()) . "<br>";
        $db->rollback();
        echo "⚠ Transação revertida<br>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
