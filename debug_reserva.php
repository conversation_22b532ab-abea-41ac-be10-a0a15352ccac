<?php
// Debug da página de reserva de emergência

echo "<h1>Debug - Reserva de Emergência</h1>";

// Passo 1: Testar includes básicos
echo "<h2>Passo 1: Includes</h2>";
try {
    require_once 'includes/auth.php';
    echo "✅ auth.php carregado<br>";
} catch (Exception $e) {
    echo "❌ Erro no auth.php: " . $e->getMessage() . "<br>";
    exit;
}

try {
    require_once 'config/database.php';
    echo "✅ database.php carregado<br>";
} catch (Exception $e) {
    echo "❌ Erro no database.php: " . $e->getMessage() . "<br>";
    exit;
}

// Passo 2: Verificar login
echo "<h2>Passo 2: Verificação de Login</h2>";
try {
    requireLogin();
    echo "✅ Usuário está logado<br>";
    echo "User ID: " . $_SESSION['user_id'] . "<br>";
} catch (Exception $e) {
    echo "❌ Erro no login: " . $e->getMessage() . "<br>";
    exit;
}

// Passo 3: Conexão com banco
echo "<h2>Passo 3: Conexão com Banco</h2>";
try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Conexão retornou null");
    }
    
    echo "✅ Conexão estabelecida<br>";
    $user_id = $_SESSION['user_id'];
} catch (Exception $e) {
    echo "❌ Erro na conexão: " . $e->getMessage() . "<br>";
    exit;
}

// Passo 4: Verificar tabelas
echo "<h2>Passo 4: Verificação de Tabelas</h2>";
try {
    $query = "SHOW TABLES LIKE 'reserva_emergencia'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "✅ Tabela reserva_emergencia existe<br>";
        
        // Testar consulta na tabela
        $query = "SELECT COUNT(*) as total FROM reserva_emergencia WHERE usuario_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✅ Consulta executada. Registros do usuário: " . $result['total'] . "<br>";
        
    } else {
        echo "❌ Tabela reserva_emergencia NÃO existe<br>";
        echo "<a href='update_database.php'>Criar tabelas</a><br>";
    }
} catch (Exception $e) {
    echo "❌ Erro ao verificar tabelas: " . $e->getMessage() . "<br>";
}

// Passo 5: Testar busca de dados
echo "<h2>Passo 5: Busca de Dados</h2>";
try {
    $query = "SELECT valor_atual, meta_valor FROM reserva_emergencia WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $reserva = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$reserva) {
        echo "⚠️ Nenhum registro encontrado, usando valores padrão<br>";
        $reserva = [
            'valor_atual' => 0,
            'meta_valor' => 0
        ];
    } else {
        echo "✅ Dados encontrados:<br>";
        echo "Valor atual: R$ " . number_format($reserva['valor_atual'], 2, ',', '.') . "<br>";
        echo "Meta: R$ " . number_format($reserva['meta_valor'], 2, ',', '.') . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Erro na busca: " . $e->getMessage() . "<br>";
    $reserva = ['valor_atual' => 0, 'meta_valor' => 0];
}

echo "<h2>Resultado Final</h2>";
echo "<p>Se chegou até aqui, a lógica básica está funcionando!</p>";
echo "<p><a href='reserva-emergencia.php'>Testar página completa</a></p>";
echo "<p><a href='dashboard.php'>Voltar ao Dashboard</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Debug Reserva</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h2 { color: #666; border-bottom: 1px solid #ccc; }
    </style>
</head>
<body>
</body>
</html>
