<?php
/**
 * Script de Instalação do E1Dividas
 * Execute este arquivo apenas uma vez para configurar o sistema
 */

// Verificar se já foi instalado
if (file_exists('config/installed.lock')) {
    die('Sistema já foi instalado. Remova o arquivo config/installed.lock para reinstalar.');
}

$message = '';
$message_type = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;

if ($_POST) {
    if (isset($_POST['action']) && $_POST['action'] == 'install') {
        try {
            // Configurações do banco
            $host = $_POST['db_host'];
            $dbname = $_POST['db_name'];
            $username = $_POST['db_user'];
            $password = $_POST['db_pass'];
            
            // Testar conexão
            $pdo = new PDO("mysql:host=$host", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Criar banco se não existir
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8 COLLATE utf8_general_ci");
            $pdo->exec("USE `$dbname`");
            
            // Criar tabelas diretamente
            $tables = [
                // Tabela de usuários
                "CREATE TABLE IF NOT EXISTS usuarios (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    nome VARCHAR(100) NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    senha VARCHAR(255) NOT NULL,
                    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    ativo BOOLEAN DEFAULT TRUE
                )",

                // Tabela de dívidas
                "CREATE TABLE IF NOT EXISTS dividas (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    usuario_id INT NOT NULL,
                    titulo VARCHAR(100) NOT NULL,
                    descricao TEXT,
                    valor DECIMAL(10,2) NOT NULL,
                    data_vencimento DATE,
                    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    quitada BOOLEAN DEFAULT FALSE,
                    data_quitacao TIMESTAMP NULL,
                    categoria VARCHAR(50),
                    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
                )",

                // Tabela de rendas
                "CREATE TABLE IF NOT EXISTS rendas (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    usuario_id INT NOT NULL,
                    titulo VARCHAR(100) NOT NULL,
                    descricao TEXT,
                    valor DECIMAL(10,2) NOT NULL,
                    tipo ENUM('fixo', 'variavel') DEFAULT 'fixo',
                    frequencia ENUM('mensal', 'semanal', 'anual', 'unico') DEFAULT 'mensal',
                    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    ativo BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
                )",

                // Índices
                "CREATE INDEX IF NOT EXISTS idx_dividas_usuario ON dividas(usuario_id)",
                "CREATE INDEX IF NOT EXISTS idx_dividas_quitada ON dividas(quitada)",
                "CREATE INDEX IF NOT EXISTS idx_rendas_usuario ON rendas(usuario_id)",
                "CREATE INDEX IF NOT EXISTS idx_rendas_ativo ON rendas(ativo)"
            ];

            foreach ($tables as $sql) {
                try {
                    $pdo->exec($sql);
                } catch (PDOException $e) {
                    throw new Exception("Erro ao criar tabela: " . $e->getMessage());
                }
            }
            
            // Atualizar arquivo de configuração
            $config_content = "<?php
/**
 * Configuração do Banco de Dados - E1Dividas
 */

class Database {
    private \$host = '$host';
    private \$db_name = '$dbname';
    private \$username = '$username';
    private \$password = '$password';
    private \$conn;

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$this->conn = new PDO(
                \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name,
                \$this->username,
                \$this->password
            );
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            \$this->conn->exec(\"set names utf8\");
        } catch(PDOException \$exception) {
            echo \"Erro de conexão: \" . \$exception->getMessage();
        }
        
        return \$this->conn;
    }
}
?>";
            
            file_put_contents('config/database.php', $config_content);
            
            // Criar usuário administrador
            $admin_nome = $_POST['admin_nome'];
            $admin_email = $_POST['admin_email'];
            $admin_senha = password_hash($_POST['admin_senha'], PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("INSERT INTO usuarios (nome, email, senha) VALUES (?, ?, ?)");
            $stmt->execute([$admin_nome, $admin_email, $admin_senha]);
            
            // Criar arquivo de lock
            if (!is_dir('config')) {
                mkdir('config', 0755, true);
            }
            file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
            
            $message = 'Sistema instalado com sucesso! Você pode fazer login agora.';
            $message_type = 'success';
            $step = 3;
            
        } catch (Exception $e) {
            $message = 'Erro na instalação: ' . $e->getMessage();
            $message_type = 'danger';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalação - E1Dividas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #FE7743;
            --secondary-color: #273F4F;
            --accent-color: #447D9B;
        }
        
        body {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 600px;
        }
        
        .install-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-logo h2 {
            color: var(--secondary-color);
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #e5663a;
            border-color: #e5663a;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            background-color: #e9ecef;
            color: #6c757d;
            font-weight: bold;
        }
        
        .step.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .step.completed {
            background-color: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <div class="install-card">
        <div class="install-logo">
            <h2><i class="fas fa-chart-line me-2"></i>E1Dividas</h2>
            <p class="text-muted">Instalação do Sistema</p>
        </div>
        
        <!-- Indicador de Passos -->
        <div class="step-indicator">
            <div class="step <?php echo $step >= 1 ? 'active' : ''; ?>">1</div>
            <div class="step <?php echo $step >= 2 ? 'active' : ''; ?>">2</div>
            <div class="step <?php echo $step >= 3 ? 'completed' : ''; ?>">3</div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?>"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($step == 1): ?>
            <!-- Passo 1: Bem-vindo -->
            <div class="text-center">
                <h4>Bem-vindo ao E1Dividas!</h4>
                <p class="text-muted mb-4">
                    Este assistente irá configurar seu sistema de controle financeiro.
                    Certifique-se de ter as informações do banco de dados MySQL em mãos.
                </p>
                
                <div class="alert alert-info text-start">
                    <h6><i class="fas fa-info-circle me-2"></i>Requisitos:</h6>
                    <ul class="mb-0">
                        <li>PHP 7.4 ou superior</li>
                        <li>MySQL 5.7 ou superior</li>
                        <li>Extensão PDO habilitada</li>
                        <li>Permissões de escrita na pasta do projeto</li>
                    </ul>
                </div>
                
                <a href="?step=2" class="btn btn-primary btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>Começar Instalação
                </a>
            </div>
            
        <?php elseif ($step == 2): ?>
            <!-- Passo 2: Configuração -->
            <h4 class="mb-4">Configuração do Sistema</h4>
            
            <form method="POST">
                <input type="hidden" name="action" value="install">
                
                <h6 class="text-muted mb-3">Configurações do Banco de Dados</h6>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="db_host" class="form-label">Host do Banco</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" 
                                   value="localhost" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="db_name" class="form-label">Nome do Banco</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" 
                                   value="e1dividas" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="db_user" class="form-label">Usuário</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" 
                                   value="root" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="db_pass" class="form-label">Senha</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass">
                        </div>
                    </div>
                </div>
                
                <hr>
                <h6 class="text-muted mb-3">Conta do Administrador</h6>
                
                <div class="mb-3">
                    <label for="admin_nome" class="form-label">Nome Completo</label>
                    <input type="text" class="form-control" id="admin_nome" name="admin_nome" required>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="admin_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="admin_senha" class="form-label">Senha</label>
                            <input type="password" class="form-control" id="admin_senha" name="admin_senha" 
                                   minlength="6" required>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                    <a href="?step=1" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Voltar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-cog me-2"></i>Instalar Sistema
                    </button>
                </div>
            </form>
            
        <?php elseif ($step == 3): ?>
            <!-- Passo 3: Concluído -->
            <div class="text-center">
                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                <h4 class="text-success">Instalação Concluída!</h4>
                <p class="text-muted mb-4">
                    O sistema E1Dividas foi instalado com sucesso. 
                    Você já pode começar a usar o sistema.
                </p>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Importante:</strong> Por segurança, remova ou renomeie o arquivo 
                    <code>install.php</code> após a instalação.
                </div>
                
                <a href="login.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>Fazer Login
                </a>
            </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
