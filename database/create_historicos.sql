-- <PERSON><PERSON><PERSON> tabela de históricos para fechamento mensal
CREATE TABLE IF NOT EXISTS historicos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    mes_referencia VARCHAR(7) NOT NULL,
    total_dividas DECIMAL(10,2) DEFAULT 0,
    total_pendente DECIMAL(10,2) DEFAULT 0,
    total_quitado DECIMAL(10,2) DEFAULT 0,
    dividas_pendentes INT DEFAULT 0,
    dividas_quitadas INT DEFAULT 0,
    total_rendas DECIMAL(10,2) DEFAULT 0,
    quantidade_rendas INT DEFAULT 0,
    data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
);

-- Índices para performance
CREATE INDEX idx_historicos_usuario ON historicos(usuario_id);
CREATE INDEX idx_historicos_mes ON historicos(mes_referencia);
CREATE INDEX idx_historicos_data ON historicos(data_fechamento);
