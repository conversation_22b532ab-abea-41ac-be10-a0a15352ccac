<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Teste Fechamento Mensal com Reserva de Emergência</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Usuário logado: ID " . $user_id . "<br>";
    
    $mes_referencia = date('Y-m') . '-TESTE-' . time();
    echo "✓ Mês de teste: " . $mes_referencia . "<br>";
    
    // Simular exatamente o que acontece no fechamento mensal
    echo "<h3>Simulando Fechamento Mensal</h3>";
    
    try {
        // Criar tabela de históricos se não existir (fora da transação)
        $db->exec("CREATE TABLE IF NOT EXISTS historicos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            mes_referencia VARCHAR(7) NOT NULL,
            total_dividas DECIMAL(10,2) DEFAULT 0,
            total_pendente DECIMAL(10,2) DEFAULT 0,
            total_quitado DECIMAL(10,2) DEFAULT 0,
            dividas_pendentes INT DEFAULT 0,
            dividas_quitadas INT DEFAULT 0,
            total_rendas DECIMAL(10,2) DEFAULT 0,
            quantidade_rendas INT DEFAULT 0,
            reserva_emergencia DECIMAL(10,2) DEFAULT 0,
            data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Adicionar coluna reserva_emergencia se não existir
        try {
            $db->exec("ALTER TABLE historicos ADD COLUMN reserva_emergencia DECIMAL(10,2) DEFAULT 0");
            echo "✓ Coluna reserva_emergencia adicionada<br>";
        } catch (Exception $e) {
            echo "✓ Coluna reserva_emergencia já existe<br>";
        }
        
        // Verificar se já existe fechamento para este mês
        $query = "SELECT COUNT(*) as total FROM historicos WHERE usuario_id = :user_id AND mes_referencia = :mes_referencia";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':mes_referencia', $mes_referencia);
        $stmt->execute();
        $existe_fechamento = $stmt->fetch(PDO::FETCH_ASSOC)['total'] > 0;
        
        if ($existe_fechamento) {
            echo "⚠ Já existe fechamento para este mês de teste<br>";
        } else {
            echo "✓ Não existe fechamento para este mês de teste<br>";
        }
        
        // Iniciar transação APÓS verificações e criação da tabela
        $db->beginTransaction();
        echo "✓ Transação iniciada<br>";
        
        // Buscar dados do mês para histórico
        $query = "SELECT
            COUNT(*) as total_dividas,
            COALESCE(SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END), 0) as total_pendente,
            COALESCE(SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END), 0) as total_quitado,
            COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
            COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas
        FROM dividas WHERE usuario_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $dados_dividas = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Dados das dívidas coletados<br>";
        
        // Buscar dados das rendas
        $query = "SELECT
            COUNT(*) as total_rendas,
            COALESCE(SUM(valor), 0) as total_renda_mensal
        FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $dados_rendas = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Dados das rendas coletados<br>";
        
        // Buscar dados da reserva de emergência - EXATAMENTE como no código original
        echo "<h4>🔍 Buscando Reserva de Emergência</h4>";
        $query = "SELECT COALESCE(valor_atual, 0) as valor_reserva 
                 FROM reserva_emergencia WHERE usuario_id = :user_id";
        echo "Query: " . $query . "<br>";
        echo "User ID: " . $user_id . "<br>";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $dados_reserva = $stmt->fetch(PDO::FETCH_ASSOC);
        $valor_reserva = $dados_reserva ? $dados_reserva['valor_reserva'] : 0;
        
        echo "Resultado da query: " . ($dados_reserva ? 'Encontrado' : 'Não encontrado') . "<br>";
        if ($dados_reserva) {
            echo "Dados retornados: " . print_r($dados_reserva, true) . "<br>";
        }
        echo "Valor final da reserva: R$ " . number_format($valor_reserva, 2, ',', '.') . "<br>";
        
        // Tratar valores NULL para evitar erros
        $total_pendente = $dados_dividas['total_pendente'] ?? 0;
        $total_quitado = $dados_dividas['total_quitado'] ?? 0;
        $dividas_pendentes = $dados_dividas['dividas_pendentes'] ?? 0;
        $dividas_quitadas = $dados_dividas['dividas_quitadas'] ?? 0;
        $total_rendas = $dados_rendas['total_renda_mensal'] ?? 0;
        $quantidade_rendas = $dados_rendas['total_rendas'] ?? 0;
        
        echo "<h4>📊 Valores para Inserção</h4>";
        echo "- Total pendente: R$ " . number_format($total_pendente, 2, ',', '.') . "<br>";
        echo "- Total quitado: R$ " . number_format($total_quitado, 2, ',', '.') . "<br>";
        echo "- Dívidas pendentes: " . $dividas_pendentes . "<br>";
        echo "- Dívidas quitadas: " . $dividas_quitadas . "<br>";
        echo "- Total rendas: R$ " . number_format($total_rendas, 2, ',', '.') . "<br>";
        echo "- Quantidade rendas: " . $quantidade_rendas . "<br>";
        echo "- <strong>Reserva emergência: R$ " . number_format($valor_reserva, 2, ',', '.') . "</strong><br>";
        
        // Inserir histórico
        $total_dividas = $total_pendente + $total_quitado;
        
        $query = "INSERT INTO historicos
            (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
             dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas, reserva_emergencia)
            VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                    :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas, :reserva_emergencia)";
        
        echo "<h4>💾 Inserindo Histórico</h4>";
        echo "Query de inserção preparada<br>";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':mes_referencia', $mes_referencia);
        $stmt->bindParam(':total_dividas', $total_dividas);
        $stmt->bindParam(':total_pendente', $total_pendente);
        $stmt->bindParam(':total_quitado', $total_quitado);
        $stmt->bindParam(':dividas_pendentes', $dividas_pendentes);
        $stmt->bindParam(':dividas_quitadas', $dividas_quitadas);
        $stmt->bindParam(':total_rendas', $total_rendas);
        $stmt->bindParam(':quantidade_rendas', $quantidade_rendas);
        $stmt->bindParam(':reserva_emergencia', $valor_reserva);
        
        echo "Parâmetros vinculados<br>";
        echo "Valor da reserva sendo inserido: R$ " . number_format($valor_reserva, 2, ',', '.') . "<br>";
        
        if (!$stmt->execute()) {
            throw new Exception('Erro ao inserir histórico: ' . implode(', ', $stmt->errorInfo()));
        }
        echo "✓ Histórico inserido com sucesso<br>";
        
        $historico_id = $db->lastInsertId();
        echo "ID do histórico inserido: " . $historico_id . "<br>";
        
        // Verificar se foi inserido corretamente
        $query = "SELECT reserva_emergencia FROM historicos WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $historico_id);
        $stmt->execute();
        $verificacao = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($verificacao) {
            echo "✓ Verificação: Reserva salva como R$ " . number_format($verificacao['reserva_emergencia'], 2, ',', '.') . "<br>";
        } else {
            echo "❌ Erro na verificação<br>";
        }
        
        $db->commit();
        echo "✓ Transação confirmada<br>";
        
        echo "<h2 style='color: green;'>✅ TESTE CONCLUÍDO COM SUCESSO!</h2>";
        echo "<p>O fechamento mensal está funcionando corretamente com a reserva de emergência.</p>";
        
        // Limpar teste
        $query = "DELETE FROM historicos WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $historico_id);
        $stmt->execute();
        echo "✓ Registro de teste removido<br>";
        
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
            echo "⚠ Transação revertida<br>";
        }
        throw $e;
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO NO TESTE:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
