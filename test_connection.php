<?php
// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Teste de Conexão - E1Dividas</h1>";

try {
    echo "<h2>1. Testando inclusão do arquivo de configuração...</h2>";
    require_once 'config/database.php';
    echo "✓ Arquivo de configuração carregado com sucesso<br>";

    echo "<h2>2. Testando criação da instância do banco...</h2>";
    $database = new Database();
    echo "✓ Instância do banco criada com sucesso<br>";

    echo "<h2>3. Testando conexão com o banco...</h2>";
    $db = $database->getConnection();
    if ($db) {
        echo "✓ Conexão com banco estabelecida com sucesso<br>";
    } else {
        echo "✗ Falha na conexão com o banco<br>";
        exit;
    }

    echo "<h2>4. Testando query simples...</h2>";
    $stmt = $db->query("SELECT 1 as test");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Query de teste executada com sucesso: " . $result['test'] . "<br>";

    echo "<h2>5. Verificando tabelas existentes...</h2>";
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "✓ Tabelas encontradas:<br>";
    foreach ($tables as $table) {
        echo "- " . $table . "<br>";
    }

    echo "<h2>6. Testando tabela usuarios...</h2>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM usuarios");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Tabela usuarios OK - Total de usuários: " . $result['total'] . "<br>";

    echo "<h2>7. Testando tabela dividas...</h2>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM dividas");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Tabela dividas OK - Total de dívidas: " . $result['total'] . "<br>";

    echo "<h2>8. Testando tabela rendas...</h2>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM rendas");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Tabela rendas OK - Total de rendas: " . $result['total'] . "<br>";

    echo "<h2>9. Verificando se tabela historicos existe...</h2>";
    $stmt = $db->query("SHOW TABLES LIKE 'historicos'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        echo "✓ Tabela historicos existe<br>";
    } else {
        echo "⚠ Tabela historicos não existe<br>";
    }

    echo "<h2 style='color: green;'>✓ Todos os testes passaram! O sistema está funcionando.</h2>";

} catch (Exception $e) {
    echo "<h2 style='color: red;'>✗ Erro encontrado:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
