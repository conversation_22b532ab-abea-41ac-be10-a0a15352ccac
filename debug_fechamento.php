<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

echo "<h2>Debug do Fechamento Mensal</h2>";

try {
    echo "<h3>1. Verificando conexão com banco...</h3>";
    $db->query("SELECT 1");
    echo "✓ Conexão OK<br>";

    echo "<h3>2. Verificando tabela dividas...</h3>";
    $query = "SELECT COUNT(*) as total FROM dividas WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Tabela dividas OK - Total: " . $result['total'] . " dívidas<br>";

    echo "<h3>3. Verificando tabela rendas...</h3>";
    $query = "SELECT COUNT(*) as total FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Tabela rendas OK - Total: " . $result['total'] . " rendas ativas<br>";

    echo "<h3>4. Testando query de dados das dívidas...</h3>";
    $query = "SELECT
        COUNT(*) as total_dividas,
        SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END) as total_pendente,
        SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END) as total_quitado,
        COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
        COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas
    FROM dividas WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dados_dividas = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Query dívidas OK<br>";
    echo "Dados: " . print_r($dados_dividas, true) . "<br>";

    echo "<h3>5. Testando query de dados das rendas...</h3>";
    $query = "SELECT
        COUNT(*) as total_rendas,
        SUM(valor) as total_renda_mensal
    FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dados_rendas = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Query rendas OK<br>";
    echo "Dados: " . print_r($dados_rendas, true) . "<br>";

    echo "<h3>6. Verificando se tabela historicos existe...</h3>";
    $query = "SHOW TABLES LIKE 'historicos'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "✓ Tabela historicos existe<br>";
    } else {
        echo "⚠ Tabela historicos não existe - será criada<br>";
    }

    echo "<h3>7. Testando criação da tabela historicos...</h3>";
    $db->exec("CREATE TABLE IF NOT EXISTS historicos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        mes_referencia VARCHAR(7) NOT NULL,
        total_dividas DECIMAL(10,2) DEFAULT 0,
        total_pendente DECIMAL(10,2) DEFAULT 0,
        total_quitado DECIMAL(10,2) DEFAULT 0,
        dividas_pendentes INT DEFAULT 0,
        dividas_quitadas INT DEFAULT 0,
        total_rendas DECIMAL(10,2) DEFAULT 0,
        quantidade_rendas INT DEFAULT 0,
        data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
    )");
    echo "✓ Tabela historicos criada/verificada<br>";

    echo "<h3>8. Testando inserção de histórico...</h3>";
    $mes_referencia = date('Y-m');
    
    // Tratar valores NULL
    $total_pendente = $dados_dividas['total_pendente'] ?? 0;
    $total_quitado = $dados_dividas['total_quitado'] ?? 0;
    $dividas_pendentes = $dados_dividas['dividas_pendentes'] ?? 0;
    $dividas_quitadas = $dados_dividas['dividas_quitadas'] ?? 0;
    $total_rendas = $dados_rendas['total_renda_mensal'] ?? 0;
    $quantidade_rendas = $dados_rendas['total_rendas'] ?? 0;

    $query = "INSERT INTO historicos
        (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
         dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas)
        VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':mes_referencia', $mes_referencia);
    $stmt->bindParam(':total_dividas', $total_pendente + $total_quitado);
    $stmt->bindParam(':total_pendente', $total_pendente);
    $stmt->bindParam(':total_quitado', $total_quitado);
    $stmt->bindParam(':dividas_pendentes', $dividas_pendentes);
    $stmt->bindParam(':dividas_quitadas', $dividas_quitadas);
    $stmt->bindParam(':total_rendas', $total_rendas);
    $stmt->bindParam(':quantidade_rendas', $quantidade_rendas);
    $stmt->execute();
    echo "✓ Inserção de histórico OK<br>";

    echo "<h3>9. Testando atualização de dívidas quitadas...</h3>";
    $query = "UPDATE dividas SET quitada = 0, data_quitacao = NULL WHERE usuario_id = :user_id AND quitada = 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    echo "✓ Atualização de dívidas OK<br>";

    echo "<h3>10. Testando transação completa...</h3>";
    $db->beginTransaction();
    
    // Repetir as operações dentro da transação
    $query = "INSERT INTO historicos
        (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
         dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas)
        VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':mes_referencia', $mes_referencia);
    $stmt->bindParam(':total_dividas', $total_pendente + $total_quitado);
    $stmt->bindParam(':total_pendente', $total_pendente);
    $stmt->bindParam(':total_quitado', $total_quitado);
    $stmt->bindParam(':dividas_pendentes', $dividas_pendentes);
    $stmt->bindParam(':dividas_quitadas', $dividas_quitadas);
    $stmt->bindParam(':total_rendas', $total_rendas);
    $stmt->bindParam(':quantidade_rendas', $quantidade_rendas);
    $stmt->execute();

    $query = "UPDATE dividas SET quitada = 0, data_quitacao = NULL WHERE usuario_id = :user_id AND quitada = 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();

    $db->commit();
    echo "✓ Transação completa OK<br>";

    echo "<h2 style='color: green;'>✓ Todos os testes passaram! O fechamento mensal deve funcionar.</h2>";

} catch (Exception $e) {
    echo "<h2 style='color: red;'>✗ Erro encontrado:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
