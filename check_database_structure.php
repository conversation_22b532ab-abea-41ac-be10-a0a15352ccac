<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Verificação da Estrutura do Banco de Dados</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Conexão estabelecida - User ID: " . $user_id . "<br>";
    
    // Verificar tabelas existentes
    echo "<h3>1. Tabelas existentes no banco</h3>";
    $query = "SHOW TABLES";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "- " . $table . "<br>";
    }
    
    // Verificar estrutura da tabela usuarios
    if (in_array('usuarios', $tables)) {
        echo "<h3>2. Estrutura da tabela usuarios</h3>";
        $query = "DESCRIBE usuarios";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")" . 
                 ($column['Key'] == 'PRI' ? ' [PRIMARY KEY]' : '') . 
                 ($column['Null'] == 'NO' ? ' [NOT NULL]' : '') . "<br>";
        }
    }
    
    // Verificar estrutura da tabela dividas
    if (in_array('dividas', $tables)) {
        echo "<h3>3. Estrutura da tabela dividas</h3>";
        $query = "DESCRIBE dividas";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")" . 
                 ($column['Key'] == 'PRI' ? ' [PRIMARY KEY]' : '') . 
                 ($column['Null'] == 'NO' ? ' [NOT NULL]' : '') . "<br>";
        }
        
        // Contar dívidas do usuário
        $query = "SELECT COUNT(*) as total FROM dividas WHERE usuario_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $total_dividas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "Total de dívidas do usuário: " . $total_dividas . "<br>";
    }
    
    // Verificar estrutura da tabela rendas
    if (in_array('rendas', $tables)) {
        echo "<h3>4. Estrutura da tabela rendas</h3>";
        $query = "DESCRIBE rendas";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")" . 
                 ($column['Key'] == 'PRI' ? ' [PRIMARY KEY]' : '') . 
                 ($column['Null'] == 'NO' ? ' [NOT NULL]' : '') . "<br>";
        }
        
        // Contar rendas do usuário
        $query = "SELECT COUNT(*) as total FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $total_rendas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "Total de rendas ativas do usuário: " . $total_rendas . "<br>";
    }
    
    // Verificar estrutura da tabela historicos
    if (in_array('historicos', $tables)) {
        echo "<h3>5. Estrutura da tabela historicos</h3>";
        $query = "DESCRIBE historicos";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")" . 
                 ($column['Key'] == 'PRI' ? ' [PRIMARY KEY]' : '') . 
                 ($column['Null'] == 'NO' ? ' [NOT NULL]' : '') . "<br>";
        }
        
        // Contar históricos do usuário
        $query = "SELECT COUNT(*) as total FROM historicos WHERE usuario_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $total_historicos = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "Total de históricos do usuário: " . $total_historicos . "<br>";
    } else {
        echo "<h3>5. Tabela historicos não existe</h3>";
        echo "A tabela será criada automaticamente no primeiro fechamento.<br>";
    }
    
    // Verificar versão do MySQL
    echo "<h3>6. Informações do servidor</h3>";
    $query = "SELECT VERSION() as version";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $version = $stmt->fetch(PDO::FETCH_ASSOC)['version'];
    echo "Versão do MySQL: " . $version . "<br>";
    
    // Verificar charset
    $query = "SELECT @@character_set_database as charset, @@collation_database as collation";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $charset_info = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Charset do banco: " . $charset_info['charset'] . "<br>";
    echo "Collation do banco: " . $charset_info['collation'] . "<br>";
    
    echo "<h2 style='color: green;'>✓ Verificação da estrutura concluída!</h2>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO NA VERIFICAÇÃO:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
}
?>
