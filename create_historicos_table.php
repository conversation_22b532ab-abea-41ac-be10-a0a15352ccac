<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Criando tabela historicos...</h2>";
    
    // Criar tabela historicos
    $sql = "CREATE TABLE IF NOT EXISTS historicos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        mes_referencia VARCHAR(7) NOT NULL,
        total_dividas DECIMAL(10,2) DEFAULT 0,
        total_pendente DECIMAL(10,2) DEFAULT 0,
        total_quitado DECIMAL(10,2) DEFAULT 0,
        dividas_pendentes INT DEFAULT 0,
        dividas_quitadas INT DEFAULT 0,
        total_rendas DECIMAL(10,2) DEFAULT 0,
        quantidade_rendas INT DEFAULT 0,
        data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
    )";
    
    $db->exec($sql);
    echo "✓ Tabela historicos criada com sucesso!<br>";
    
    // Criar índices
    $db->exec("CREATE INDEX IF NOT EXISTS idx_historicos_usuario ON historicos(usuario_id)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_historicos_mes ON historicos(mes_referencia)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_historicos_data ON historicos(data_fechamento)");
    
    echo "✓ Índices criados com sucesso!<br>";
    echo "<h3 style='color: green;'>Tabela historicos está pronta para uso!</h3>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Erro ao criar tabela:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
}
?>
