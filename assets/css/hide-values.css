/* Estilos para o sistema de Hide/Show Values */

.value-hidden {
    font-family: monospace;
    letter-spacing: 2px;
    color: inherit !important;
}

#toggleValues {
    transition: all 0.3s ease;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 0.875rem;
    font-weight: 500;
}

#toggleValues:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

#toggleValues i {
    transition: transform 0.3s ease;
}

#toggleValues:hover i {
    transform: scale(1.1);
}

/* Animação suave para transição de valores */
.value-display, .value-hidden {
    transition: opacity 0.2s ease-in-out;
}

/* Estilo para valores ocultos em diferentes contextos */
.stats-card .value-hidden,
.card .value-hidden {
    color: inherit;
}

/* Responsividade */
@media (max-width: 768px) {
    #toggleValues {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
    
    #toggleText {
        display: none;
    }
}

/* Indicador visual quando valores estão ocultos */
body.values-hidden {
    position: relative;
}

body.values-hidden::before {
    content: "🔒 Valores Ocultos";
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.75rem;
    z-index: 1000;
    pointer-events: none;
}

@media (max-width: 768px) {
    body.values-hidden::before {
        top: 5px;
        right: 5px;
        font-size: 0.7rem;
        padding: 3px 8px;
    }
}
