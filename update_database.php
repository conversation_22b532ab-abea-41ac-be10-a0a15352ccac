<?php
/**
 * Script para atualizar o banco de dados com as tabelas da Reserva de Emergência
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Atualizando banco de dados...</h2>";
    
    // Criar tabela reserva_emergencia
    $sql1 = "CREATE TABLE IF NOT EXISTS reserva_emergencia (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        valor_atual DECIMAL(10,2) DEFAULT 0.00,
        meta_valor DECIMAL(10,2) DEFAULT 0.00,
        data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
    )";
    
    $db->exec($sql1);
    echo "✅ Tabela 'reserva_emergencia' criada com sucesso!<br>";
    
    // Criar tabela movimentacoes_reserva
    $sql2 = "CREATE TABLE IF NOT EXISTS movimentacoes_reserva (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        tipo ENUM('deposito', 'retirada') NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        descricao TEXT,
        data_movimentacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
    )";
    
    $db->exec($sql2);
    echo "✅ Tabela 'movimentacoes_reserva' criada com sucesso!<br>";
    
    // Criar índices
    $indices = [
        "CREATE INDEX IF NOT EXISTS idx_reserva_usuario ON reserva_emergencia(usuario_id)",
        "CREATE INDEX IF NOT EXISTS idx_movimentacoes_usuario ON movimentacoes_reserva(usuario_id)",
        "CREATE INDEX IF NOT EXISTS idx_movimentacoes_data ON movimentacoes_reserva(data_movimentacao)"
    ];
    
    foreach ($indices as $indice) {
        $db->exec($indice);
    }
    echo "✅ Índices criados com sucesso!<br>";
    
    echo "<br><h3>✅ Banco de dados atualizado com sucesso!</h3>";
    echo "<p><a href='dashboard.php'>Ir para o Dashboard</a></p>";
    echo "<p><a href='reserva-emergencia.php'>Testar Reserva de Emergência</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Erro ao atualizar banco de dados:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    
    // Mostrar detalhes do erro para debug
    echo "<h4>Detalhes do erro:</h4>";
    echo "<pre>";
    echo "Código: " . $e->getCode() . "\n";
    echo "Arquivo: " . $e->getFile() . "\n";
    echo "Linha: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
    echo "</pre>";
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Atualização do Banco de Dados - E1Dividas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Atualização do Banco de Dados - E1Dividas</h4>
                    </div>
                    <div class="card-body">
                        <!-- O conteúdo PHP será exibido aqui -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
