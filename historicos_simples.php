<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

$historicos = [];
$error_message = '';

try {
    // Verificar se a tabela historicos existe
    $query = "SHOW TABLES LIKE 'historicos'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Criar tabela de históricos
        $createTable = "CREATE TABLE historicos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            mes_referencia VARCHAR(7) NOT NULL,
            total_dividas DECIMAL(10,2) DEFAULT 0,
            total_pendente DECIMAL(10,2) DEFAULT 0,
            total_quitado DECIMAL(10,2) DEFAULT 0,
            dividas_pendentes INT DEFAULT 0,
            dividas_quitadas INT DEFAULT 0,
            total_rendas DECIMAL(10,2) DEFAULT 0,
            quantidade_rendas INT DEFAULT 0,
            data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $db->exec($createTable);
        $error_message = 'Tabela de históricos criada. Realize um fechamento mensal para ver dados aqui.';
    }
    
    // Buscar históricos
    $query = "SELECT * FROM historicos WHERE usuario_id = ? ORDER BY mes_referencia DESC";
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $historicos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = 'Erro ao carregar históricos: ' . $e->getMessage();
}

$page_title = 'Históricos';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-history me-2"></i>Históricos Financeiros</h1>
            <a href="dividas.php" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Voltar às Dívidas
            </a>
        </div>
    </div>
</div>

<?php if ($error_message): ?>
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i><?php echo $error_message; ?>
</div>
<?php endif; ?>

<!-- Cards de Resumo -->
<?php if (!empty($historicos)): ?>
<?php
$total_gasto = 0;
$total_renda = 0;
foreach ($historicos as $h) {
    $total_gasto += floatval($h['total_quitado']);
    $total_renda += floatval($h['total_rendas']);
}
$total_economia = $total_renda - $total_gasto;
?>
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <h5><i class="fas fa-money-bill-alt me-2"></i>Total Gasto</h5>
                <h3>R$ <?php echo number_format($total_gasto, 2, ',', '.'); ?></h3>
                <p class="mb-0">Histórico completo</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <h5><i class="fas fa-coins me-2"></i>Total Renda</h5>
                <h3>R$ <?php echo number_format($total_renda, 2, ',', '.'); ?></h3>
                <p class="mb-0">Histórico completo</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white <?php echo $total_economia >= 0 ? 'bg-info' : 'bg-warning'; ?>">
            <div class="card-body">
                <h5><i class="fas fa-chart-line me-2"></i><?php echo $total_economia >= 0 ? 'Economia' : 'Déficit'; ?></h5>
                <h3>R$ <?php echo number_format(abs($total_economia), 2, ',', '.'); ?></h3>
                <p class="mb-0"><?php echo count($historicos); ?> fechamento(s)</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Lista de Históricos -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Histórico de Fechamentos</h5>
    </div>
    <div class="card-body">
        <?php if (empty($historicos)): ?>
            <div class="text-center py-5">
                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Nenhum histórico encontrado</h5>
                <p class="text-muted">Realize um fechamento mensal na página de dívidas para começar.</p>
                <a href="dividas.php" class="btn btn-primary">
                    <i class="fas fa-credit-card me-2"></i>Ir para Dívidas
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Mês/Ano</th>
                            <th>Dívidas</th>
                            <th>Gastos</th>
                            <th>Rendas</th>
                            <th>Saldo</th>
                            <th>Data Fechamento</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($historicos as $historico): ?>
                        <?php
                        $saldo_mes = floatval($historico['total_rendas']) - floatval($historico['total_quitado']);
                        $classe_saldo = $saldo_mes >= 0 ? 'text-success' : 'text-danger';
                        ?>
                        <tr>
                            <td>
                                <strong><?php echo $historico['mes_referencia']; ?></strong>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo intval($historico['dividas_pendentes']) + intval($historico['dividas_quitadas']); ?> total</span>
                                <br><small class="text-success"><?php echo $historico['dividas_quitadas']; ?> quitadas</small>
                                <br><small class="text-warning"><?php echo $historico['dividas_pendentes']; ?> pendentes</small>
                            </td>
                            <td>
                                <strong class="text-danger">R$ <?php echo number_format(floatval($historico['total_quitado']), 2, ',', '.'); ?></strong>
                            </td>
                            <td>
                                <strong class="text-success">R$ <?php echo number_format(floatval($historico['total_rendas']), 2, ',', '.'); ?></strong>
                                <br><small class="text-muted"><?php echo $historico['quantidade_rendas']; ?> fonte(s)</small>
                            </td>
                            <td>
                                <strong class="<?php echo $classe_saldo; ?>">
                                    R$ <?php echo number_format(abs($saldo_mes), 2, ',', '.'); ?>
                                </strong>
                                <br><small class="text-muted">
                                    <?php echo $saldo_mes >= 0 ? 'Economia' : 'Déficit'; ?>
                                </small>
                            </td>
                            <td>
                                <?php echo date('d/m/Y H:i', strtotime($historico['data_fechamento'])); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php if (!empty($historicos)): ?>
<!-- Informações Adicionais -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Como funciona o Fechamento Mensal:</h6>
            <ul class="mb-0">
                <li>Salva um histórico completo do mês atual</li>
                <li>Registra todas as dívidas (quitadas e pendentes)</li>
                <li>Registra todas as fontes de renda ativas</li>
                <li>Calcula o saldo do mês (rendas - gastos)</li>
                <li>Reseta dívidas quitadas para "Pendente" para o próximo mês</li>
                <li>Mantém todas as dívidas cadastradas</li>
            </ul>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
