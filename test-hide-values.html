<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Hide Values</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/hide-values.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Teste do Sistema Hide/Show Values</h1>
        
        <!-- Botão Toggle -->
        <div class="row mb-3">
            <div class="col-12 text-end">
                <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleValues">
                    <i class="fas fa-eye" id="toggleIcon"></i>
                    <span id="toggleText">Ocultar Valores</span>
                </button>
            </div>
        </div>
        
        <!-- Valores de Teste -->
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5>Teste 1</h5>
                        <h3>
                            <span class="value-display">R$ 1.234,56</span>
                            <span class="value-hidden" style="display: none;">R$ •••,••</span>
                        </h3>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5>Teste 2</h5>
                        <h3>
                            <span class="value-display">R$ 5.678,90</span>
                            <span class="value-hidden" style="display: none;">R$ •••,••</span>
                        </h3>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5>Teste 3</h5>
                        <h3>
                            <span class="value-display">R$ 9.876,54</span>
                            <span class="value-hidden" style="display: none;">R$ •••,••</span>
                        </h3>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h4>Console de Debug</h4>
            <div id="debug" class="alert alert-info">
                Abra o console do navegador (F12) para ver os logs de debug.
            </div>
        </div>
    </div>

    <script>
        console.log('Teste: Script carregado');
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Teste: DOM carregado');
            
            const toggleButton = document.getElementById('toggleValues');
            console.log('Teste: Botão encontrado?', !!toggleButton);
            
            if (toggleButton) {
                toggleButton.addEventListener('click', function() {
                    console.log('Teste: Botão clicado!');
                    
                    const valueDisplays = document.querySelectorAll('.value-display');
                    const valueHiddens = document.querySelectorAll('.value-hidden');
                    const toggleIcon = document.getElementById('toggleIcon');
                    const toggleText = document.getElementById('toggleText');
                    
                    console.log('Teste: Elementos encontrados:', {
                        displays: valueDisplays.length,
                        hiddens: valueHiddens.length,
                        icon: !!toggleIcon,
                        text: !!toggleText
                    });
                    
                    if (valueDisplays.length > 0) {
                        const isHidden = valueDisplays[0].style.display === 'none';
                        console.log('Teste: Estado atual isHidden:', isHidden);
                        
                        if (isHidden) {
                            // Mostrar valores
                            valueDisplays.forEach(el => el.style.display = 'inline');
                            valueHiddens.forEach(el => el.style.display = 'none');
                            if (toggleIcon) toggleIcon.className = 'fas fa-eye';
                            if (toggleText) toggleText.textContent = 'Ocultar Valores';
                            console.log('Teste: Valores mostrados');
                        } else {
                            // Ocultar valores
                            valueDisplays.forEach(el => el.style.display = 'none');
                            valueHiddens.forEach(el => el.style.display = 'inline');
                            if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
                            if (toggleText) toggleText.textContent = 'Mostrar Valores';
                            console.log('Teste: Valores ocultos');
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
