# Solução para Erro 500 no Fechamento Mensal

## Problema Identificado
O erro 500 no fechamento mensal estava sendo causado por valores NULL nas consultas SQL que não estavam sendo tratados adequadamente.

## Correções Implementadas

### 1. Tratamento de Valores NULL
- Adicionado `COALESCE()` nas consultas SQL para tratar valores NULL
- Implementado tratamento de valores NULL com operador `??` no PHP
- Valores padrão definidos como 0 para evitar erros

### 2. Validações Adicionais
- Validação do formato do mês de referência
- Verificação se já existe fechamento para o mês
- Melhor tratamento de erros com mensagens específicas

### 3. Melhorias na Transação
- Verificação de sucesso em cada operação
- Rollback automático em caso de erro
- Mensagens de erro mais detalhadas

## Arquivos Criados/Modificados

### Arquivos Modificados:
- `dividas.php` - Código do fechamento mensal corrigido

### Arquivos Criados:
- `debug_fechamento.php` - Script para testar o fechamento
- `create_historicos_table.php` - Script para criar a tabela historicos
- `database/create_historicos.sql` - SQL da tabela historicos

## Como Testar

### 1. Execute o script de criação da tabela:
```
http://seu-dominio/create_historicos_table.php
```

### 2. Execute o debug para verificar se tudo está funcionando:
```
http://seu-dominio/debug_fechamento.php
```

### 3. Teste o fechamento mensal na página de dívidas

## Principais Mudanças no Código

### Antes:
```php
$stmt->bindParam(':total_pendente', $dados_dividas['total_pendente']);
```

### Depois:
```php
$total_pendente = $dados_dividas['total_pendente'] ?? 0;
$stmt->bindParam(':total_pendente', $total_pendente);
```

### Antes:
```sql
SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END) as total_pendente
```

### Depois:
```sql
COALESCE(SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END), 0) as total_pendente
```

## Verificação de Funcionamento

Após aplicar as correções, o fechamento mensal deve:
1. Validar o mês de referência
2. Verificar se já existe fechamento para o mês
3. Criar a tabela historicos se não existir
4. Buscar dados das dívidas e rendas com tratamento de NULL
5. Inserir o histórico com sucesso
6. Resetar as dívidas quitadas
7. Confirmar a transação

Se ainda houver problemas, execute o `debug_fechamento.php` para identificar o erro específico.
