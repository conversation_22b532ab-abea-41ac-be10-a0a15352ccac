<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debug Passo a Passo - Fechamento Mensal</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Usuário logado: ID " . $user_id . "<br>";
    
    $mes_referencia = date('Y-m');
    echo "✓ Mês de referência: " . $mes_referencia . "<br>";
    
    // PASSO 1: Validar mês de referência
    echo "<h3>PASSO 1: Validação do mês</h3>";
    if (empty($mes_referencia) || !preg_match('/^\d{4}-\d{2}$/', $mes_referencia)) {
        throw new Exception('Mês de referência inválido.');
    }
    echo "✓ Validação OK<br>";
    
    // PASSO 2: Iniciar transação
    echo "<h3>PASSO 2: Iniciar transação</h3>";
    $db->beginTransaction();
    echo "✓ Transação iniciada<br>";
    echo "Status da transação: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
    
    // PASSO 3: Verificar fechamento existente
    echo "<h3>PASSO 3: Verificar fechamento existente</h3>";
    try {
        $query = "SELECT COUNT(*) as total FROM historicos WHERE usuario_id = :user_id AND mes_referencia = :mes_referencia";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':mes_referencia', $mes_referencia);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $existe_fechamento = $result['total'] > 0;
        
        echo "Fechamento existente: " . ($existe_fechamento ? 'SIM' : 'NÃO') . "<br>";
        
        if ($existe_fechamento) {
            echo "Status da transação antes do rollback: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
            $db->rollback();
            echo "⚠ Rollback executado - fechamento já existe<br>";
            echo "Status da transação após rollback: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
            throw new Exception('Já existe um fechamento para este mês.');
        }
        echo "✓ Verificação OK - pode prosseguir<br>";
    } catch (Exception $e) {
        echo "❌ Erro na verificação: " . $e->getMessage() . "<br>";
        throw $e;
    }
    
    // PASSO 4: Criar tabela se não existir
    echo "<h3>PASSO 4: Criar tabela historicos</h3>";
    try {
        $create_sql = "CREATE TABLE IF NOT EXISTS historicos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            mes_referencia VARCHAR(7) NOT NULL,
            total_dividas DECIMAL(10,2) DEFAULT 0,
            total_pendente DECIMAL(10,2) DEFAULT 0,
            total_quitado DECIMAL(10,2) DEFAULT 0,
            dividas_pendentes INT DEFAULT 0,
            dividas_quitadas INT DEFAULT 0,
            total_rendas DECIMAL(10,2) DEFAULT 0,
            quantidade_rendas INT DEFAULT 0,
            data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $db->exec($create_sql);
        echo "✓ Tabela historicos verificada/criada<br>";
        echo "Status da transação: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
    } catch (Exception $e) {
        echo "❌ Erro na criação da tabela: " . $e->getMessage() . "<br>";
        throw $e;
    }
    
    // PASSO 5: Buscar dados das dívidas
    echo "<h3>PASSO 5: Buscar dados das dívidas</h3>";
    try {
        $query = "SELECT
            COUNT(*) as total_dividas,
            COALESCE(SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END), 0) as total_pendente,
            COALESCE(SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END), 0) as total_quitado,
            COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
            COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas
        FROM dividas WHERE usuario_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $dados_dividas = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Dados das dívidas coletados<br>";
        echo "Status da transação: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
    } catch (Exception $e) {
        echo "❌ Erro na busca das dívidas: " . $e->getMessage() . "<br>";
        throw $e;
    }
    
    // PASSO 6: Buscar dados das rendas
    echo "<h3>PASSO 6: Buscar dados das rendas</h3>";
    try {
        $query = "SELECT
            COUNT(*) as total_rendas,
            COALESCE(SUM(valor), 0) as total_renda_mensal
        FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $dados_rendas = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Dados das rendas coletados<br>";
        echo "Status da transação: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
    } catch (Exception $e) {
        echo "❌ Erro na busca das rendas: " . $e->getMessage() . "<br>";
        throw $e;
    }
    
    // PASSO 7: Preparar dados
    echo "<h3>PASSO 7: Preparar dados para inserção</h3>";
    $total_pendente = $dados_dividas['total_pendente'] ?? 0;
    $total_quitado = $dados_dividas['total_quitado'] ?? 0;
    $dividas_pendentes = $dados_dividas['dividas_pendentes'] ?? 0;
    $dividas_quitadas = $dados_dividas['dividas_quitadas'] ?? 0;
    $total_rendas = $dados_rendas['total_renda_mensal'] ?? 0;
    $quantidade_rendas = $dados_rendas['total_rendas'] ?? 0;
    $total_dividas = $total_pendente + $total_quitado;
    
    echo "Dados preparados:<br>";
    echo "- Total dívidas: R$ " . number_format($total_dividas, 2, ',', '.') . "<br>";
    echo "- Total pendente: R$ " . number_format($total_pendente, 2, ',', '.') . "<br>";
    echo "- Total quitado: R$ " . number_format($total_quitado, 2, ',', '.') . "<br>";
    echo "- Dívidas pendentes: " . $dividas_pendentes . "<br>";
    echo "- Dívidas quitadas: " . $dividas_quitadas . "<br>";
    echo "- Total rendas: R$ " . number_format($total_rendas, 2, ',', '.') . "<br>";
    echo "- Quantidade rendas: " . $quantidade_rendas . "<br>";
    echo "Status da transação: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
    
    // PASSO 8: Inserir histórico
    echo "<h3>PASSO 8: Inserir histórico</h3>";
    try {
        $query = "INSERT INTO historicos
            (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
             dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas)
            VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                    :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':mes_referencia', $mes_referencia);
        $stmt->bindParam(':total_dividas', $total_dividas);
        $stmt->bindParam(':total_pendente', $total_pendente);
        $stmt->bindParam(':total_quitado', $total_quitado);
        $stmt->bindParam(':dividas_pendentes', $dividas_pendentes);
        $stmt->bindParam(':dividas_quitadas', $dividas_quitadas);
        $stmt->bindParam(':total_rendas', $total_rendas);
        $stmt->bindParam(':quantidade_rendas', $quantidade_rendas);
        
        if (!$stmt->execute()) {
            throw new Exception('Erro ao inserir histórico: ' . implode(', ', $stmt->errorInfo()));
        }
        echo "✓ Histórico inserido com sucesso<br>";
        echo "Status da transação: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
    } catch (Exception $e) {
        echo "❌ Erro na inserção do histórico: " . $e->getMessage() . "<br>";
        throw $e;
    }
    
    // PASSO 9: Atualizar dívidas
    echo "<h3>PASSO 9: Atualizar dívidas</h3>";
    try {
        $query = "UPDATE dividas SET quitada = 0, data_quitacao = NULL WHERE usuario_id = :user_id AND quitada = 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        
        if (!$stmt->execute()) {
            throw new Exception('Erro ao atualizar dívidas: ' . implode(', ', $stmt->errorInfo()));
        }
        echo "✓ Dívidas atualizadas (" . $stmt->rowCount() . " registros afetados)<br>";
        echo "Status da transação: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
    } catch (Exception $e) {
        echo "❌ Erro na atualização das dívidas: " . $e->getMessage() . "<br>";
        throw $e;
    }
    
    // PASSO 10: Confirmar transação
    echo "<h3>PASSO 10: Confirmar transação</h3>";
    $db->commit();
    echo "✓ Transação confirmada<br>";
    echo "Status da transação: " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "<br>";
    
    echo "<h2 style='color: green;'>✅ FECHAMENTO MENSAL REALIZADO COM SUCESSO!</h2>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO NO PASSO:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    
    if (isset($db)) {
        echo "<p><strong>Status da transação no erro:</strong> " . ($db->inTransaction() ? 'ATIVA' : 'INATIVA') . "</p>";
        
        try {
            if ($db->inTransaction()) {
                $db->rollback();
                echo "<p>✓ Rollback executado com sucesso</p>";
            }
        } catch (Exception $rollbackError) {
            echo "<p>❌ Erro no rollback: " . $rollbackError->getMessage() . "</p>";
        }
    }
}
?>
