-- Script de atualização para adicionar funcionalidade de Reserva de Emergência
-- Execute este script no seu banco de dados para adicionar as novas tabelas

-- Tabela de reserva de emergência
CREATE TABLE IF NOT EXISTS reserva_emergencia (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    valor_atual DECIMAL(10,2) DEFAULT 0.00,
    meta_valor DECIMAL(10,2) DEFAULT 0.00,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
);

-- Tabela de movimentações da reserva de emergência
CREATE TABLE IF NOT EXISTS movimentacoes_reserva (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    tipo ENUM('deposito', 'retirada') NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    descricao TEXT,
    data_movimentacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_reserva_usuario ON reserva_emergencia(usuario_id);
CREATE INDEX IF NOT EXISTS idx_movimentacoes_usuario ON movimentacoes_reserva(usuario_id);
CREATE INDEX IF NOT EXISTS idx_movimentacoes_data ON movimentacoes_reserva(data_movimentacao);
