<?php
// Teste simples para verificar se o problema é específico
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Teste Simples - E1Dividas</h1>";

// Teste 1: PHP básico
echo "<h2>1. PHP básico</h2>";
echo "PHP está funcionando!<br>";

// Teste 2: Incluir arquivos
echo "<h2>2. Incluindo arquivos</h2>";
try {
    require_once 'config/database.php';
    echo "✓ config/database.php OK<br>";
} catch (Exception $e) {
    echo "✗ Erro em config/database.php: " . $e->getMessage() . "<br>";
}

try {
    require_once 'includes/auth.php';
    echo "✓ includes/auth.php OK<br>";
} catch (Exception $e) {
    echo "✗ Erro em includes/auth.php: " . $e->getMessage() . "<br>";
}

// Teste 3: Conexão com banco
echo "<h2>3. Conexão com banco</h2>";
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "✓ Conexão OK<br>";
} catch (Exception $e) {
    echo "✗ Erro na conexão: " . $e->getMessage() . "<br>";
}

// Teste 4: Sessão
echo "<h2>4. Sessão</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
echo "✓ Sessão iniciada<br>";

if (isset($_SESSION['user_id'])) {
    echo "✓ Usuário logado: " . $_SESSION['user_id'] . "<br>";
} else {
    echo "⚠ Usuário não logado<br>";
}

echo "<h2>✓ Teste concluído!</h2>";
?>
