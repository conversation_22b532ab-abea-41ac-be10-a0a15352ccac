<?php
/**
 * EXEMPLO de Configuração do Banco de Dados - E1Dividas
 * 
 * INSTRUÇÕES:
 * 1. Copie este arquivo para config/database.php
 * 2. Altere as configurações abaixo com seus dados
 * 3. Execute install.php ou install_manual.php
 */

class Database {
    // ALTERE ESTAS CONFIGURAÇÕES:
    private $host = 'localhost';                    // Host do banco (geralmente localhost)
    private $db_name = 'u880879026_e1dividas';    // Nome do seu banco de dados
    private $username = 'u880879026_e1dividas';   // Usuário do banco
    private $password = 'SUA_SENHA_AQUI';         // Senha do banco
    
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->exec("set names utf8");
        } catch(PDOException $exception) {
            echo "Erro de conexão: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}
?>
