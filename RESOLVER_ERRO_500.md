# Resolver Erro 500 - E1Dividas

## Diagnóstico do Problema

O erro 500 indica um problema interno do servidor. Vamos diagnosticar e resolver passo a passo.

## Passos para Diagnóstico

### 1. Execute o teste simples primeiro:
```
http://e1dividas.melhorcupom.shop/simple_test.php
```

### 2. Se o teste simples funcionar, execute o debug completo:
```
http://e1dividas.melhorcupom.shop/debug_error_500.php
```

### 3. Teste a conexão com o banco:
```
http://e1dividas.melhorcupom.shop/test_connection.php
```

## Possíveis Causas e Soluções

### Causa 1: Problema de Sintaxe PHP
**Sintomas:** Erro de sintaxe nos arquivos
**Solução:** 
- Execute `debug_error_500.php` para verificar sintaxe
- Corrija erros de sintaxe identificados

### Causa 2: Problema de Conexão com Banco
**Sintomas:** Erro ao conectar com banco de dados
**Solução:**
- V<PERSON><PERSON><PERSON> as credenciais em `config/database.php`
- Teste a conexão com `test_connection.php`

### Causa 3: Problema de Sessão
**Sintomas:** Erro relacionado a sessões
**Solução:**
- Verifique se o usuário está logado
- Teste o login em `login.php`

### Causa 4: Arquivo não encontrado
**Sintomas:** Erro de include/require
**Solução:**
- Verifique se todos os arquivos existem
- Verifique permissões dos arquivos

### Causa 5: Problema de Permissões
**Sintomas:** Erro de acesso a arquivos
**Solução:**
- Configure permissões corretas (644 para arquivos, 755 para diretórios)

## Verificação de Arquivos Essenciais

Certifique-se de que estes arquivos existem:
- `config/database.php`
- `includes/auth.php`
- `includes/header.php`
- `includes/footer.php`
- `dividas.php`

## Teste de Funcionamento

Após resolver o problema:

1. Teste o login: `http://e1dividas.melhorcupom.shop/login.php`
2. Teste o dashboard: `http://e1dividas.melhorcupom.shop/dashboard.php`
3. Teste as dívidas: `http://e1dividas.melhorcupom.shop/dividas.php`

## Logs do Servidor

Se o problema persistir, verifique os logs do servidor web:
- Apache: `/var/log/apache2/error.log`
- Nginx: `/var/log/nginx/error.log`

## Contato para Suporte

Se nenhuma das soluções funcionar, forneça:
1. Resultado do `simple_test.php`
2. Resultado do `debug_error_500.php`
3. Logs de erro do servidor
4. Mensagem de erro específica
