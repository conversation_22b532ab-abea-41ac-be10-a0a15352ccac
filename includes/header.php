<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>E1Dividas</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Hide Values CSS -->
    <link href="assets/css/hide-values.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #FE7743;
            --secondary-color: #273F4F;
            --accent-color: #447D9B;
            --light-color: #D7D7D7;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background-color: var(--secondary-color) !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: white !important;
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .navbar-nav .nav-link {
            color: white !important;
            margin: 0 10px;
            transition: color 0.3s;
        }
        
        .navbar-nav .nav-link:hover {
            color: var(--primary-color) !important;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #e5663a;
            border-color: #e5663a;
        }
        
        .btn-secondary {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }
        
        .btn-secondary:hover {
            background-color: #3a6b85;
            border-color: #3a6b85;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        
        .card-header {
            background-color: var(--secondary-color);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
        
        .sidebar {
            background-color: var(--secondary-color);
            min-height: calc(100vh - 56px);
            padding: 20px 0;
        }
        
        .sidebar .nav-link {
            color: white;
            padding: 15px 20px;
            margin: 5px 0;
            border-radius: 0;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-card h3 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-card p {
            margin: 0;
            opacity: 0.9;
        }
        
        .table th {
            background-color: var(--secondary-color);
            color: white;
            border: none;
        }
        
        .badge-quitada {
            background-color: #28a745;
        }
        
        .badge-pendente {
            background-color: var(--primary-color);
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
        }
        
        .login-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo h2 {
            color: var(--secondary-color);
            font-weight: bold;
        }
        
        /* Estilos específicos para tabs */
        .nav-tabs .nav-link {
            color: var(--secondary-color);
            border: 1px solid transparent;
        }
        
        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
            border-bottom: 1px solid #fff;
        }
        
        .nav-tabs .nav-link:hover {
            color: var(--primary-color);
            border-color: #e9ecef #e9ecef #dee2e6;
        }

        /* Classe para 5 colunas no dashboard */
        .col-md-2-4 {
            flex: 0 0 auto;
            width: 20%;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .login-card {
                margin: 20px;
                padding: 30px 20px;
            }

            .sidebar {
                min-height: auto;
            }

            .col-md-2-4 {
                width: 100%;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>

<?php if (isset($_SESSION['user_id'])): ?>
<!-- Navbar -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="dashboard.php">
            <i class="fas fa-chart-line me-2"></i>E1Dividas
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="perfil.php"><i class="fas fa-user-cog me-2"></i>Perfil</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-2 p-0">
            <div class="sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dividas.php' ? 'active' : ''; ?>" href="dividas.php">
                        <i class="fas fa-credit-card"></i>Minhas Dívidas
                    </a>
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'rendas.php' ? 'active' : ''; ?>" href="rendas.php">
                        <i class="fas fa-money-bill-wave"></i>Minha Renda
                    </a>
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'reserva-emergencia.php' ? 'active' : ''; ?>" href="reserva-emergencia.php">
                        <i class="fas fa-piggy-bank"></i>Reserva de Emergência
                    </a>
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'historicos.php' ? 'active' : ''; ?>" href="historicos.php">
                        <i class="fas fa-history"></i>Históricos
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-10">
            <div class="main-content">
<?php endif; ?>
