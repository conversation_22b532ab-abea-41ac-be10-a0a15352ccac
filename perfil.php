<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

$message = '';
$message_type = '';

// Processar atualização do perfil
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'update_profile') {
    $nome = trim($_POST['nome']);
    $email = trim($_POST['email']);
    $senha_atual = $_POST['senha_atual'];
    $nova_senha = $_POST['nova_senha'];
    $confirmar_senha = $_POST['confirmar_senha'];
    
    if (empty($nome) || empty($email)) {
        $message = 'Nome e email são obrigatórios.';
        $message_type = 'danger';
    } elseif (!isValidEmail($email)) {
        $message = 'Email inválido.';
        $message_type = 'danger';
    } else {
        // Verificar se o email já está em uso por outro usuário
        $query = "SELECT id FROM usuarios WHERE email = :email AND id != :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $message = 'Este email já está em uso por outro usuário.';
            $message_type = 'danger';
        } else {
            // Atualizar dados básicos
            $query = "UPDATE usuarios SET nome = :nome, email = :email WHERE id = :user_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':nome', $nome);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':user_id', $user_id);
            
            if ($stmt->execute()) {
                $_SESSION['user_name'] = $nome;
                $_SESSION['user_email'] = $email;
                
                // Se foi fornecida nova senha, atualizar
                if (!empty($nova_senha)) {
                    if (empty($senha_atual)) {
                        $message = 'Para alterar a senha, informe a senha atual.';
                        $message_type = 'danger';
                    } elseif (!isValidPassword($nova_senha)) {
                        $message = 'A nova senha deve ter pelo menos 6 caracteres.';
                        $message_type = 'danger';
                    } elseif ($nova_senha !== $confirmar_senha) {
                        $message = 'A confirmação da nova senha não confere.';
                        $message_type = 'danger';
                    } else {
                        // Verificar senha atual
                        $query = "SELECT senha FROM usuarios WHERE id = :user_id";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->execute();
                        $user_data = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if (password_verify($senha_atual, $user_data['senha'])) {
                            $nova_senha_hash = password_hash($nova_senha, PASSWORD_DEFAULT);
                            
                            $query = "UPDATE usuarios SET senha = :senha WHERE id = :user_id";
                            $stmt = $db->prepare($query);
                            $stmt->bindParam(':senha', $nova_senha_hash);
                            $stmt->bindParam(':user_id', $user_id);
                            
                            if ($stmt->execute()) {
                                $message = 'Perfil e senha atualizados com sucesso!';
                                $message_type = 'success';
                            } else {
                                $message = 'Erro ao atualizar a senha.';
                                $message_type = 'danger';
                            }
                        } else {
                            $message = 'Senha atual incorreta.';
                            $message_type = 'danger';
                        }
                    }
                } else {
                    $message = 'Perfil atualizado com sucesso!';
                    $message_type = 'success';
                }
            } else {
                $message = 'Erro ao atualizar perfil.';
                $message_type = 'danger';
            }
        }
    }
}

// Buscar dados do usuário
$query = "SELECT nome, email, data_criacao FROM usuarios WHERE id = :user_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$user_data = $stmt->fetch(PDO::FETCH_ASSOC);

$page_title = 'Perfil';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-user-cog me-2"></i>Meu Perfil
        </h1>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Editar Perfil</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nome" class="form-label">Nome Completo *</label>
                                <input type="text" class="form-control" id="nome" name="nome" 
                                       value="<?php echo htmlspecialchars($user_data['nome']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user_data['email']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    <h6 class="text-muted mb-3">Alterar Senha (opcional)</h6>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="senha_atual" class="form-label">Senha Atual</label>
                                <input type="password" class="form-control" id="senha_atual" name="senha_atual">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="nova_senha" class="form-label">Nova Senha</label>
                                <input type="password" class="form-control" id="nova_senha" name="nova_senha">
                                <div class="form-text">Mínimo de 6 caracteres</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="confirmar_senha" class="form-label">Confirmar Nova Senha</label>
                                <input type="password" class="form-control" id="confirmar_senha" name="confirmar_senha">
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="dashboard.php" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Voltar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informações da Conta</h5>
            </div>
            <div class="card-body">
                <p><strong>Membro desde:</strong><br>
                   <?php echo date('d/m/Y', strtotime($user_data['data_criacao'])); ?>
                </p>
                
                <p><strong>Último acesso:</strong><br>
                   Agora
                </p>
                
                <hr>
                
                <div class="d-grid">
                    <a href="logout.php" class="btn btn-outline-danger" 
                       onclick="return confirm('Tem certeza que deseja sair?')">
                        <i class="fas fa-sign-out-alt me-2"></i>Sair da Conta
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Estatísticas Rápidas</h5>
            </div>
            <div class="card-body">
                <?php
                // Buscar estatísticas rápidas
                $query = "SELECT 
                    (SELECT COUNT(*) FROM dividas WHERE usuario_id = :user_id) as total_dividas,
                    (SELECT COUNT(*) FROM rendas WHERE usuario_id = :user_id) as total_rendas";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                $stats = $stmt->fetch(PDO::FETCH_ASSOC);
                ?>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary"><?php echo $stats['total_dividas']; ?></h4>
                        <small class="text-muted">Dívidas</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success"><?php echo $stats['total_rendas']; ?></h4>
                        <small class="text-muted">Rendas</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
