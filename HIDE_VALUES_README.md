# Sistema Hide/Show Values - E1Dividas

## 📋 Visão Geral

O sistema Hide/Show Values foi implementado para aumentar a segurança e privacidade dos dados financeiros no E1Dividas. Permite que os usuários ocultem temporariamente todos os valores monetários da interface por questões de segurança.

## 🔒 Funcionalidades

### ✅ Recursos Implementados

- **Botão Toggle**: Botão "Ocultar/Mostrar Valores" em todas as páginas principais
- **Persistência**: Estado salvo no localStorage do navegador
- **Atalho de Teclado**: `Ctrl + H` para alternar rapidamente
- **Indicador Visual**: Ícone de cadeado quando valores estão ocultos
- **Responsivo**: Interface adaptada para dispositivos móveis
- **Animações**: Transições suaves entre estados

### 📱 Páginas Cobertas

1. **Dashboard** (`dashboard.php`)
   - Cards de estatísticas
   - Resumo financeiro
   - Gráficos (valores ocultos)

2. **Dívidas** (`dividas.php`)
   - Cards de resumo
   - Tabela de dívidas
   - Modal de fechamento mensal

3. **Rendas** (`rendas.php`)
   - Cards de resumo
   - Tabela de rendas
   - Valores mensais equivalentes

4. **Históricos** (`historicos.php`)
   - Cards de resumo geral
   - Tabela de históricos
   - Valores de saldo

## 🛠️ Implementação Técnica

### Arquivos Criados/Modificados

```
assets/
├── css/hide-values.css     # Estilos do sistema
└── js/hide-values.js       # Lógica JavaScript

includes/
└── header.php              # Inclusão dos assets

páginas principais:
├── dashboard.php
├── dividas.php
├── rendas.php
└── historicos.php
```

### Estrutura HTML

```html
<!-- Botão Toggle -->
<button type="button" class="btn btn-outline-secondary btn-sm" id="toggleValues" onclick="toggleValuesVisibility()">
    <i class="fas fa-eye" id="toggleIcon"></i>
    <span id="toggleText">Ocultar Valores</span>
</button>

<!-- Valores com Hide/Show -->
<span class="value-display">R$ 1.234,56</span>
<span class="value-hidden" style="display: none;">R$ •••,••</span>
```

### Classes CSS

- `.value-display`: Valores reais (visíveis por padrão)
- `.value-hidden`: Valores ocultos (•••,••)
- `#toggleValues`: Botão de alternância
- `.values-hidden`: Classe aplicada ao body quando oculto

## 🎯 Como Usar

### Para Usuários

1. **Ocultar Valores**:
   - Clique no botão "Ocultar Valores" (ícone de olho)
   - Use o atalho `Ctrl + H`

2. **Mostrar Valores**:
   - Clique no botão "Mostrar Valores" (ícone de olho cortado)
   - Use o atalho `Ctrl + H` novamente

3. **Estado Persistente**:
   - A preferência é salva automaticamente
   - Mantém o estado entre páginas e sessões

### Para Desenvolvedores

#### Adicionar Hide/Show a Novos Valores

```html
<!-- Substitua isso: -->
<span>R$ <?php echo number_format($valor, 2, ',', '.'); ?></span>

<!-- Por isso: -->
<span class="value-display">R$ <?php echo number_format($valor, 2, ',', '.'); ?></span>
<span class="value-hidden" style="display: none;">R$ •••,••</span>
```

#### Adicionar Botão Toggle

```html
<div class="row mb-3">
    <div class="col-12 text-end">
        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleValues" onclick="toggleValuesVisibility()">
            <i class="fas fa-eye" id="toggleIcon"></i>
            <span id="toggleText">Ocultar Valores</span>
        </button>
    </div>
</div>
```

## 🔧 Configurações

### LocalStorage

- **Chave**: `valuesHidden`
- **Valores**: `'true'` | `'false'`
- **Escopo**: Por domínio/origem

### Eventos Personalizados

```javascript
// Escutar quando valores são ocultos
document.addEventListener('valuesHidden', function() {
    console.log('Valores foram ocultos');
});

// Escutar quando valores são mostrados
document.addEventListener('valuesShown', function() {
    console.log('Valores foram mostrados');
});
```

## 🎨 Personalização

### Modificar Texto Oculto

No arquivo `hide-values.js`, altere a função para usar texto personalizado:

```javascript
// Em vez de "R$ •••,••", usar outro padrão
valueHiddens.forEach(el => el.textContent = 'R$ ***,**');
```

### Modificar Atalho de Teclado

```javascript
// Alterar de Ctrl+H para Ctrl+Shift+H
if (e.ctrlKey && e.shiftKey && e.key === 'H') {
    e.preventDefault();
    toggleValuesVisibility();
}
```

## 🔐 Segurança

### Considerações

- ✅ Valores ocultos apenas visualmente (não removidos do DOM)
- ✅ Estado salvo localmente (não enviado ao servidor)
- ✅ Funciona offline
- ⚠️ Não protege contra inspeção de código-fonte
- ⚠️ Não protege contra ferramentas de desenvolvedor

### Recomendações

- Use em ambientes onde outras pessoas possam ver a tela
- Não substitui medidas de segurança adequadas
- Combine com outras práticas de segurança

## 📱 Responsividade

### Desktop
- Botão com texto completo
- Indicador visual no canto superior direito

### Mobile
- Botão apenas com ícone (texto oculto)
- Indicador visual reduzido

## 🐛 Troubleshooting

### Problemas Comuns

1. **Botão não funciona**:
   - Verificar se `assets/js/hide-values.js` está carregado
   - Verificar console do navegador por erros

2. **Estado não persiste**:
   - Verificar se localStorage está habilitado
   - Limpar cache do navegador

3. **Valores não ocultam**:
   - Verificar se classes `.value-display` e `.value-hidden` estão aplicadas
   - Verificar se CSS está carregado

### Debug

```javascript
// Verificar estado atual
console.log('Values hidden:', localStorage.getItem('valuesHidden'));

// Verificar elementos
console.log('Display elements:', document.querySelectorAll('.value-display').length);
console.log('Hidden elements:', document.querySelectorAll('.value-hidden').length);
```

## 📈 Melhorias Futuras

- [ ] Opção de senha para mostrar valores
- [ ] Timeout automático (ocultar após X minutos)
- [ ] Configurações por usuário no banco de dados
- [ ] Diferentes níveis de ocultação
- [ ] Integração com sistema de permissões
