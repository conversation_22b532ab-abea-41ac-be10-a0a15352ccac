<?php
require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

$message = '';
$message_type = '';

// Buscar todas as dívidas do usuário
$query = "SELECT * FROM dividas WHERE usuario_id = :user_id ORDER BY quitada ASC, data_vencimento ASC, data_criacao DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$dividas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calcular totais
$total_dividas = 0;
$total_quitadas = 0;
$total_pendentes = 0;
$count_quitadas = 0;
$count_pendentes = 0;

foreach ($dividas as $divida) {
    $total_dividas += $divida['valor'];
    if ($divida['quitada']) {
        $total_quitadas += $divida['valor'];
        $count_quitadas++;
    } else {
        $total_pendentes += $divida['valor'];
        $count_pendentes++;
    }
}

$page_title = 'Minhas Dívidas';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-credit-card me-2"></i>Minhas Dívidas</h1>
    </div>
</div>

<!-- Cards de Resumo -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #FE7743, #e5663a);">
            <div class="card-body">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Pendentes</h5>
                <h3>R$ <?php echo number_format($total_pendentes, 2, ',', '.'); ?></h3>
                <p class="mb-0"><?php echo $count_pendentes; ?> dívida(s)</p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #28a745, #20c997);">
            <div class="card-body">
                <h5><i class="fas fa-check-circle me-2"></i>Quitadas</h5>
                <h3>R$ <?php echo number_format($total_quitadas, 2, ',', '.'); ?></h3>
                <p class="mb-0"><?php echo $count_quitadas; ?> dívida(s)</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #273F4F, #495057);">
            <div class="card-body">
                <h5><i class="fas fa-calculator me-2"></i>Total Geral</h5>
                <h3>R$ <?php echo number_format($total_dividas, 2, ',', '.'); ?></h3>
                <p class="mb-0"><?php echo count($dividas); ?> dívida(s)</p>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Dívidas -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Lista de Dívidas</h5>
    </div>
    <div class="card-body">
        <?php if (empty($dividas)): ?>
            <div class="text-center py-5">
                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Nenhuma dívida cadastrada</h5>
                <p class="text-muted">Clique no botão "Nova Dívida" para começar.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Título</th>
                            <th>Categoria</th>
                            <th>Valor</th>
                            <th>Vencimento</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($dividas as $divida): ?>
                        <tr class="<?php echo $divida['quitada'] ? 'table-success' : ''; ?>">
                            <td>
                                <strong><?php echo htmlspecialchars($divida['titulo']); ?></strong>
                                <?php if ($divida['descricao']): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($divida['descricao']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($divida['categoria']): ?>
                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($divida['categoria']); ?></span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong>R$ <?php echo number_format($divida['valor'], 2, ',', '.'); ?></strong>
                            </td>
                            <td>
                                <?php if ($divida['data_vencimento']): ?>
                                    <?php echo date('d/m/Y', strtotime($divida['data_vencimento'])); ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($divida['quitada']): ?>
                                    <span class="badge badge-quitada">
                                        <i class="fas fa-check me-1"></i>Quitada
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-pendente">
                                        <i class="fas fa-clock me-1"></i>Pendente
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
