<?php
/**
 * Sistema de Autenticação - E1Dividas
 */

session_start();

// Função para verificar se o usuário está logado
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Função para redirecionar usuários não autenticados
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

// Função para fazer logout
function logout() {
    session_destroy();
    header('Location: login.php');
    exit();
}

// Função para fazer login
function login($email, $senha) {
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT id, nome, email, senha FROM usuarios WHERE email = :email AND ativo = 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (password_verify($senha, $user['senha'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['nome'];
            $_SESSION['user_email'] = $user['email'];
            return true;
        }
    }
    
    return false;
}

// Função para registrar novo usuário
function register($nome, $email, $senha) {
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Verificar se o email já existe
    $query = "SELECT id FROM usuarios WHERE email = :email";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        return false; // Email já existe
    }
    
    // Criar novo usuário
    $senha_hash = password_hash($senha, PASSWORD_DEFAULT);
    
    $query = "INSERT INTO usuarios (nome, email, senha) VALUES (:nome, :email, :senha)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':nome', $nome);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':senha', $senha_hash);
    
    return $stmt->execute();
}

// Função para obter dados do usuário atual
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'nome' => $_SESSION['user_name'],
        'email' => $_SESSION['user_email']
    ];
}

// Função para validar email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Função para validar senha (mínimo 6 caracteres)
function isValidPassword($senha) {
    return strlen($senha) >= 6;
}
?>
