<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Teste de Atualização de Datas de Vencimento</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Usuário logado: ID " . $user_id . "<br>";
    
    // Buscar dívidas atuais para demonstrar
    echo "<h3>1. Dívidas Atuais (antes da simulação)</h3>";
    $query = "SELECT id, titulo, data_vencimento, quitada FROM dividas WHERE usuario_id = :user_id ORDER BY titulo";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dividas_antes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($dividas_antes)) {
        echo "⚠ Nenhuma dívida encontrada para demonstrar<br>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Título</th><th>Data Vencimento</th><th>Status</th></tr>";
    foreach ($dividas_antes as $divida) {
        $status = $divida['quitada'] ? 'Quitada' : 'Pendente';
        $data_formatada = $divida['data_vencimento'] ? date('d/m/Y', strtotime($divida['data_vencimento'])) : 'Não definida';
        echo "<tr>";
        echo "<td>" . $divida['id'] . "</td>";
        echo "<td>" . htmlspecialchars($divida['titulo']) . "</td>";
        echo "<td>" . $data_formatada . "</td>";
        echo "<td>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Demonstrar como a query funciona
    echo "<h3>2. Demonstração da Lógica de Atualização</h3>";
    $mes_ano_atual = date('Y-m');
    echo "Mês/Ano atual: " . $mes_ano_atual . "<br>";
    
    echo "<h4>Exemplos de como as datas seriam atualizadas:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Data Original</th><th>Dia Extraído</th><th>Nova Data</th><th>Explicação</th></tr>";
    
    $exemplos = [
        '2024-01-15' => 'Dia 15 de Janeiro vira dia 15 do mês atual',
        '2024-02-28' => 'Dia 28 de Fevereiro vira dia 28 do mês atual',
        '2024-03-05' => 'Dia 5 de Março vira dia 5 do mês atual',
        '2024-12-31' => 'Dia 31 de Dezembro vira dia 31 do mês atual (se possível)',
        null => 'Sem data original vira dia 1 do mês atual'
    ];
    
    foreach ($exemplos as $data_original => $explicacao) {
        if ($data_original) {
            $dia = date('d', strtotime($data_original));
            $nova_data = $mes_ano_atual . '-' . str_pad($dia, 2, '0', STR_PAD_LEFT);
            $data_original_formatada = date('d/m/Y', strtotime($data_original));
            $nova_data_formatada = date('d/m/Y', strtotime($nova_data));
        } else {
            $dia = '01';
            $nova_data = $mes_ano_atual . '-01';
            $data_original_formatada = 'Não definida';
            $nova_data_formatada = date('d/m/Y', strtotime($nova_data));
        }
        
        echo "<tr>";
        echo "<td>" . $data_original_formatada . "</td>";
        echo "<td>" . $dia . "</td>";
        echo "<td>" . $nova_data_formatada . "</td>";
        echo "<td>" . $explicacao . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Testar a query real (sem executar)
    echo "<h3>3. Query SQL que será executada</h3>";
    echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo "<pre>";
    echo "UPDATE dividas SET 
    quitada = 0, 
    data_quitacao = NULL, 
    data_vencimento = CASE 
        WHEN data_vencimento IS NOT NULL THEN 
            CONCAT('$mes_ano_atual', '-', LPAD(DAY(data_vencimento), 2, '0'))
        ELSE 
            CONCAT('$mes_ano_atual', '-01')
    END
WHERE usuario_id = $user_id AND quitada = 1";
    echo "</pre>";
    echo "</div>";
    
    // Simular o resultado (sem alterar dados reais)
    echo "<h3>4. Simulação do Resultado</h3>";
    echo "<p><strong>Dívidas que seriam afetadas:</strong> Apenas as que estão quitadas</p>";
    
    $dividas_quitadas = array_filter($dividas_antes, function($divida) {
        return $divida['quitada'] == 1;
    });
    
    if (empty($dividas_quitadas)) {
        echo "⚠ Nenhuma dívida quitada encontrada para demonstrar<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Título</th><th>Data Atual</th><th>Nova Data</th><th>Mudança</th></tr>";
        
        foreach ($dividas_quitadas as $divida) {
            $data_atual = $divida['data_vencimento'];
            $data_atual_formatada = $data_atual ? date('d/m/Y', strtotime($data_atual)) : 'Não definida';
            
            if ($data_atual) {
                $dia = date('d', strtotime($data_atual));
                $nova_data = $mes_ano_atual . '-' . str_pad($dia, 2, '0', STR_PAD_LEFT);
            } else {
                $nova_data = $mes_ano_atual . '-01';
            }
            
            $nova_data_formatada = date('d/m/Y', strtotime($nova_data));
            
            $mudanca = ($data_atual_formatada != $nova_data_formatada) ? 
                       "Alterada" : "Sem alteração";
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($divida['titulo']) . "</td>";
            echo "<td>" . $data_atual_formatada . "</td>";
            echo "<td>" . $nova_data_formatada . "</td>";
            echo "<td>" . $mudanca . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>5. Vantagens desta Abordagem</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Mantém o dia original:</strong> Se a dívida vence dia 15, continuará vencendo dia 15</li>";
    echo "<li>✅ <strong>Atualiza mês/ano:</strong> Move para o período atual</li>";
    echo "<li>✅ <strong>Trata casos especiais:</strong> Datas sem vencimento viram dia 1</li>";
    echo "<li>✅ <strong>Preserva padrão:</strong> Usuário não precisa reconfigurar datas</li>";
    echo "<li>✅ <strong>Automático:</strong> Funciona para qualquer quantidade de dívidas</li>";
    echo "</ul>";
    
    echo "<h3>6. Exemplo Prático</h3>";
    echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; border-radius: 5px;'>";
    echo "<p><strong>Cenário:</strong> Você tem uma dívida que vence todo dia 10</p>";
    echo "<p><strong>Janeiro:</strong> Vencimento em 10/01/2024 → Você quita</p>";
    echo "<p><strong>Fechamento:</strong> Sistema move para 10/02/2024 automaticamente</p>";
    echo "<p><strong>Fevereiro:</strong> Vencimento em 10/02/2024 → Você quita</p>";
    echo "<p><strong>Fechamento:</strong> Sistema move para 10/03/2024 automaticamente</p>";
    echo "<p><strong>Resultado:</strong> Sempre vence no dia 10, sem trabalho manual!</p>";
    echo "</div>";
    
    echo "<br><p><a href='dividas.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Testar Fechamento Real</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
}
?>
