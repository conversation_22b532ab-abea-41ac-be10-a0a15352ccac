<?php
// Configurar exibição de erros para desenvolvimento
error_reporting(E_ALL);
ini_set('display_errors', 0); // Mudar para 1 se precisar debugar

require_once 'includes/auth.php';
require_once 'config/database.php';

requireLogin();

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

$historicos = [];
$total_gasto_historico = 0;
$total_renda_historico = 0;
$total_economia = 0;
$error_message = '';
$message = '';
$message_type = '';

// Processar ações
if ($_POST && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'delete_historico':
            $historico_id = intval($_POST['historico_id']);

            if ($historico_id > 0) {
                try {
                    // Verificar se o histórico pertence ao usuário
                    $query = "SELECT id FROM historicos WHERE id = :id AND usuario_id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':id', $historico_id);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();

                    if ($stmt->fetch()) {
                        // Excluir o histórico
                        $query = "DELETE FROM historicos WHERE id = :id AND usuario_id = :user_id";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':id', $historico_id);
                        $stmt->bindParam(':user_id', $user_id);

                        if ($stmt->execute()) {
                            $message = 'Histórico excluído com sucesso!';
                            $message_type = 'success';
                        } else {
                            $message = 'Erro ao excluir histórico.';
                            $message_type = 'danger';
                        }
                    } else {
                        $message = 'Histórico não encontrado ou não pertence ao usuário.';
                        $message_type = 'danger';
                    }
                } catch (Exception $e) {
                    $message = 'Erro ao excluir histórico: ' . $e->getMessage();
                    $message_type = 'danger';
                }
            } else {
                $message = 'ID do histórico inválido.';
                $message_type = 'danger';
            }
            break;
    }
}

try {
    // Verificar se a tabela historicos existe, se não, criar
    $query = "SHOW TABLES LIKE 'historicos'";
    $stmt = $db->prepare($query);
    $stmt->execute();

    if ($stmt->rowCount() == 0) {
        // Criar tabela de históricos sem foreign key para evitar problemas
        $createTable = "CREATE TABLE historicos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            mes_referencia VARCHAR(7) NOT NULL,
            total_dividas DECIMAL(10,2) DEFAULT 0,
            total_pendente DECIMAL(10,2) DEFAULT 0,
            total_quitado DECIMAL(10,2) DEFAULT 0,
            dividas_pendentes INT DEFAULT 0,
            dividas_quitadas INT DEFAULT 0,
            total_rendas DECIMAL(10,2) DEFAULT 0,
            quantidade_rendas INT DEFAULT 0,
            data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $db->exec($createTable);
    }

    // Buscar todos os históricos do usuário
    $query = "SELECT * FROM historicos WHERE usuario_id = ? ORDER BY mes_referencia DESC";
    $stmt = $db->prepare($query);
    $stmt->execute([$user_id]);
    $historicos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calcular totais gerais de forma segura
    if (!empty($historicos)) {
        foreach ($historicos as $historico) {
            $total_gasto_historico += floatval($historico['total_quitado'] ?? 0);
            $total_renda_historico += floatval($historico['total_rendas'] ?? 0);
            $total_economia += (floatval($historico['total_rendas'] ?? 0) - floatval($historico['total_quitado'] ?? 0));
        }
    }

} catch (Exception $e) {
    // Em caso de erro, continuar com arrays vazios
    $historicos = [];
    $error_message = 'Erro ao carregar históricos. Tente novamente.';
    error_log("Erro na página de históricos: " . $e->getMessage());
}

$page_title = 'Históricos';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-history me-2"></i>Históricos Financeiros</h1>
            <a href="dividas.php" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Voltar às Dívidas
            </a>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php if ($error_message): ?>
<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
</div>
<?php endif; ?>

<!-- Botão Hide/Show Values -->
<div class="row mb-3">
    <div class="col-12 text-end">
        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleValues">
            <i class="fas fa-eye" id="toggleIcon"></i>
            <span id="toggleText">Ocultar Valores</span>
        </button>
    </div>
</div>

<!-- Cards de Resumo Geral -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #dc3545, #c82333);">
            <div class="card-body">
                <h5><i class="fas fa-money-bill-alt me-2"></i>Total Gasto</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($total_gasto_historico, 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0">Histórico completo</p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, #28a745, #20c997);">
            <div class="card-body">
                <h5><i class="fas fa-coins me-2"></i>Total Renda</h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format($total_renda_historico, 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0">Histórico completo</p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card text-white" style="background: linear-gradient(135deg, <?php echo $total_economia >= 0 ? '#447D9B, #6c757d' : '#FE7743, #e5663a'; ?>);">
            <div class="card-body">
                <h5><i class="fas fa-chart-line me-2"></i><?php echo $total_economia >= 0 ? 'Economia Total' : 'Déficit Total'; ?></h5>
                <h3>
                    <span class="value-display">R$ <?php echo number_format(abs($total_economia), 2, ',', '.'); ?></span>
                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                </h3>
                <p class="mb-0"><?php echo count($historicos); ?> fechamento(s)</p>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Históricos -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Histórico de Fechamentos</h5>
    </div>
    <div class="card-body">
        <?php if (empty($historicos)): ?>
            <div class="text-center py-5">
                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Nenhum histórico encontrado</h5>
                <p class="text-muted">Realize um fechamento mensal na página de dívidas para começar.</p>
                <a href="dividas.php" class="btn btn-primary">
                    <i class="fas fa-credit-card me-2"></i>Ir para Dívidas
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Mês/Ano</th>
                            <th>Dívidas</th>
                            <th>Gastos</th>
                            <th>Rendas</th>
                            <th>Saldo</th>
                            <th>Data Fechamento</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($historicos as $historico): ?>
                        <?php
                        $saldo_mes = floatval($historico['total_rendas']) - floatval($historico['total_quitado']);
                        $classe_saldo = $saldo_mes >= 0 ? 'text-success' : 'text-danger';

                        // Formatação segura da data
                        $mes_referencia = $historico['mes_referencia'];
                        $mes_formatado = null;
                        $nome_mes = $mes_referencia;

                        try {
                            if (preg_match('/^\d{4}-\d{2}$/', $mes_referencia)) {
                                $mes_formatado = DateTime::createFromFormat('Y-m', $mes_referencia);
                                if ($mes_formatado) {
                                    $meses = ['', 'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                                             'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
                                    $nome_mes = $meses[(int)$mes_formatado->format('n')] . ' de ' . $mes_formatado->format('Y');
                                }
                            }
                        } catch (Exception $e) {
                            // Usar valor padrão em caso de erro
                        }
                        ?>
                        <tr>
                            <td>
                                <strong><?php echo $mes_formatado ? $mes_formatado->format('m/Y') : $mes_referencia; ?></strong>
                                <br><small class="text-muted"><?php echo $nome_mes; ?></small>
                            </td>
                            <td>
                                <span class="badge bg-info me-1"><?php echo $historico['dividas_pendentes'] + $historico['dividas_quitadas']; ?> total</span>
                                <br><small class="text-success"><?php echo $historico['dividas_quitadas']; ?> quitadas</small>
                                <br><small class="text-warning"><?php echo $historico['dividas_pendentes']; ?> pendentes</small>
                            </td>
                            <td>
                                <strong class="text-danger">
                                    <span class="value-display">R$ <?php echo number_format($historico['total_quitado'], 2, ',', '.'); ?></span>
                                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                                </strong>
                                <br><small class="text-muted">Dívidas quitadas</small>
                            </td>
                            <td>
                                <strong class="text-success">
                                    <span class="value-display">R$ <?php echo number_format($historico['total_rendas'], 2, ',', '.'); ?></span>
                                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                                </strong>
                                <br><small class="text-muted"><?php echo $historico['quantidade_rendas']; ?> fonte(s)</small>
                            </td>
                            <td>
                                <strong class="<?php echo $classe_saldo; ?>">
                                    <span class="value-display">R$ <?php echo number_format(abs($saldo_mes), 2, ',', '.'); ?></span>
                                    <span class="value-hidden" style="display: none;">R$ •••,••</span>
                                </strong>
                                <br><small class="text-muted">
                                    <?php echo $saldo_mes >= 0 ? 'Economia' : 'Déficit'; ?>
                                </small>
                            </td>
                            <td>
                                <?php echo date('d/m/Y H:i', strtotime($historico['data_fechamento'])); ?>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-info me-1" onclick="verDetalhes(<?php echo htmlspecialchars(json_encode($historico)); ?>)">
                                    <i class="fas fa-eye"></i> Ver
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="confirmarExclusao(<?php echo $historico['id']; ?>, '<?php echo $nome_mes; ?>')">
                                    <i class="fas fa-trash"></i> Remover
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Gráfico de Evolução -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Evolução Financeira</h6>
                        </div>
                        <div class="card-body">
                            <div style="height: 300px; position: relative;">
                                <canvas id="evolucaoChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal para Detalhes do Histórico -->
<div class="modal fade" id="detalhesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>Detalhes do Fechamento</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detalhesContent">
                <!-- Conteúdo será preenchido via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<script>
// Função para ver detalhes do histórico
function verDetalhes(historico) {
    const mesFormatado = new Date(historico.mes_referencia + '-01').toLocaleDateString('pt-BR', { 
        month: 'long', 
        year: 'numeric' 
    });
    
    const saldoMes = historico.total_rendas - historico.total_quitado;
    const classeSaldo = saldoMes >= 0 ? 'text-success' : 'text-danger';
    
    const content = `
        <div class="row">
            <div class="col-12 mb-3">
                <h6 class="text-center">Fechamento de ${mesFormatado}</h6>
                <p class="text-center text-muted">Realizado em ${new Date(historico.data_fechamento).toLocaleString('pt-BR')}</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-credit-card me-2"></i>Dívidas</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Total de Dívidas:</strong> ${historico.dividas_pendentes + historico.dividas_quitadas}</p>
                        <p><strong>Dívidas Quitadas:</strong> ${historico.dividas_quitadas}</p>
                        <p><strong>Dívidas Pendentes:</strong> ${historico.dividas_pendentes}</p>
                        <p><strong>Valor Quitado:</strong> R$ ${parseFloat(historico.total_quitado).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</p>
                        <p><strong>Valor Pendente:</strong> R$ ${parseFloat(historico.total_pendente).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Rendas</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Fontes de Renda:</strong> ${historico.quantidade_rendas}</p>
                        <p><strong>Total de Rendas:</strong> R$ ${parseFloat(historico.total_rendas).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</p>
                        <hr>
                        <p><strong>Saldo do Mês:</strong></p>
                        <h5 class="${classeSaldo}">R$ ${Math.abs(saldoMes).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</h5>
                        <small class="text-muted">${saldoMes >= 0 ? 'Economia' : 'Déficit'}</small>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('detalhesContent').innerHTML = content;
    const modal = new bootstrap.Modal(document.getElementById('detalhesModal'));
    modal.show();
}

// Gráfico de evolução (se houver dados)
<?php if (!empty($historicos)): ?>
document.addEventListener('DOMContentLoaded', function() {
    try {
        const historicosData = <?php echo json_encode(array_reverse($historicos)); ?>;

        if (historicosData && historicosData.length > 0) {
            const labels = [];
            const gastosData = [];
            const rendasData = [];
            const saldoData = [];

            historicosData.forEach(function(h) {
                // Criar label seguro
                let label = h.mes_referencia;
                try {
                    const date = new Date(h.mes_referencia + '-01');
                    if (!isNaN(date.getTime())) {
                        label = date.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
                    }
                } catch (e) {
                    // Usar valor padrão
                }

                labels.push(label);
                gastosData.push(parseFloat(h.total_quitado) || 0);
                rendasData.push(parseFloat(h.total_rendas) || 0);
                saldoData.push((parseFloat(h.total_rendas) || 0) - (parseFloat(h.total_quitado) || 0));
            });

            const ctx = document.getElementById('evolucaoChart');
            if (ctx && typeof Chart !== 'undefined') {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [
                            {
                                label: 'Gastos',
                                data: gastosData,
                                borderColor: '#dc3545',
                                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                                tension: 0.4
                            },
                            {
                                label: 'Rendas',
                                data: rendasData,
                                borderColor: '#28a745',
                                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                                tension: 0.4
                            },
                            {
                                label: 'Saldo',
                                data: saldoData,
                                borderColor: '#447D9B',
                                backgroundColor: 'rgba(68, 125, 155, 0.1)',
                                tension: 0.4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return 'R$ ' + value.toFixed(2).replace('.', ',');
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }
    } catch (error) {
        console.log('Erro ao carregar gráfico:', error);
    }
});

// Sistema de Hide/Show Values - Versão Simplificada
function toggleValuesVisibility() {
    const valueDisplays = document.querySelectorAll('.value-display');
    const valueHiddens = document.querySelectorAll('.value-hidden');
    const toggleIcon = document.getElementById('toggleIcon');
    const toggleText = document.getElementById('toggleText');

    if (valueDisplays.length === 0) return;

    const isHidden = valueDisplays[0].style.display === 'none';

    if (isHidden) {
        valueDisplays.forEach(el => el.style.display = 'inline');
        valueHiddens.forEach(el => el.style.display = 'none');
        if (toggleIcon) toggleIcon.className = 'fas fa-eye';
        if (toggleText) toggleText.textContent = 'Ocultar Valores';
        localStorage.setItem('valuesHidden', 'false');
    } else {
        valueDisplays.forEach(el => el.style.display = 'none');
        valueHiddens.forEach(el => el.style.display = 'inline');
        if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
        if (toggleText) toggleText.textContent = 'Mostrar Valores';
        localStorage.setItem('valuesHidden', 'true');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Aplicar estado salvo
    const valuesHidden = localStorage.getItem('valuesHidden') === 'true';
    if (valuesHidden) {
        const valueDisplays = document.querySelectorAll('.value-display');
        const valueHiddens = document.querySelectorAll('.value-hidden');
        const toggleIcon = document.getElementById('toggleIcon');
        const toggleText = document.getElementById('toggleText');

        if (valueDisplays.length > 0) {
            valueDisplays.forEach(el => el.style.display = 'none');
            valueHiddens.forEach(el => el.style.display = 'inline');
            if (toggleIcon) toggleIcon.className = 'fas fa-eye-slash';
            if (toggleText) toggleText.textContent = 'Mostrar Valores';
        }
    }

    // Adicionar listener ao botão
    const toggleButton = document.getElementById('toggleValues');
    if (toggleButton) {
        toggleButton.addEventListener('click', function(e) {
            e.preventDefault();
            toggleValuesVisibility();
        });
    }
});
<?php endif; ?>
</script>

<?php include 'includes/footer.php'; ?>
