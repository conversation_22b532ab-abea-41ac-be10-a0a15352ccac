<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debug do Fechamento Mensal - Erro 500</h2>";

try {
    require_once 'includes/auth.php';
    echo "✓ Auth incluído<br>";
    
    require_once 'config/database.php';
    echo "✓ Database incluído<br>";
    
    requireLogin();
    echo "✓ Login verificado<br>";
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    echo "✓ Conexão estabelecida - User ID: " . $user_id . "<br>";
    
    // Simular o fechamento mensal
    $mes_referencia = date('Y-m');
    echo "✓ Mês de referência: " . $mes_referencia . "<br>";
    
    // Verificar se tabela historicos existe
    $query = "SHOW TABLES LIKE 'historicos'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$result) {
        echo "⚠ Tabela historicos não existe - criando...<br>";
        $db->exec("CREATE TABLE IF NOT EXISTS historicos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            mes_referencia VARCHAR(7) NOT NULL,
            total_dividas DECIMAL(10,2) DEFAULT 0,
            total_pendente DECIMAL(10,2) DEFAULT 0,
            total_quitado DECIMAL(10,2) DEFAULT 0,
            dividas_pendentes INT DEFAULT 0,
            dividas_quitadas INT DEFAULT 0,
            total_rendas DECIMAL(10,2) DEFAULT 0,
            quantidade_rendas INT DEFAULT 0,
            data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
        )");
        echo "✓ Tabela historicos criada<br>";
    } else {
        echo "✓ Tabela historicos existe<br>";
    }
    
    // Testar query de verificação de fechamento
    $query = "SELECT COUNT(*) as total FROM historicos WHERE usuario_id = :user_id AND mes_referencia = :mes_referencia";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':mes_referencia', $mes_referencia);
    $stmt->execute();
    $existe_fechamento = $stmt->fetch(PDO::FETCH_ASSOC)['total'] > 0;
    echo "✓ Verificação de fechamento OK - Existe: " . ($existe_fechamento ? 'Sim' : 'Não') . "<br>";
    
    // Testar query de dados das dívidas
    $query = "SELECT
        COUNT(*) as total_dividas,
        COALESCE(SUM(CASE WHEN quitada = 0 THEN valor ELSE 0 END), 0) as total_pendente,
        COALESCE(SUM(CASE WHEN quitada = 1 THEN valor ELSE 0 END), 0) as total_quitado,
        COUNT(CASE WHEN quitada = 0 THEN 1 END) as dividas_pendentes,
        COUNT(CASE WHEN quitada = 1 THEN 1 END) as dividas_quitadas
    FROM dividas WHERE usuario_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dados_dividas = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Query dívidas OK<br>";
    echo "Dados dívidas: " . print_r($dados_dividas, true) . "<br>";
    
    // Testar query de dados das rendas
    $query = "SELECT
        COUNT(*) as total_rendas,
        COALESCE(SUM(valor), 0) as total_renda_mensal
    FROM rendas WHERE usuario_id = :user_id AND ativo = 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dados_rendas = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Query rendas OK<br>";
    echo "Dados rendas: " . print_r($dados_rendas, true) . "<br>";
    
    // Testar inserção de histórico
    $total_pendente = $dados_dividas['total_pendente'] ?? 0;
    $total_quitado = $dados_dividas['total_quitado'] ?? 0;
    $dividas_pendentes = $dados_dividas['dividas_pendentes'] ?? 0;
    $dividas_quitadas = $dados_dividas['dividas_quitadas'] ?? 0;
    $total_rendas = $dados_rendas['total_renda_mensal'] ?? 0;
    $quantidade_rendas = $dados_rendas['total_rendas'] ?? 0;
    
    echo "Valores tratados:<br>";
    echo "- Total pendente: " . $total_pendente . "<br>";
    echo "- Total quitado: " . $total_quitado . "<br>";
    echo "- Dívidas pendentes: " . $dividas_pendentes . "<br>";
    echo "- Dívidas quitadas: " . $dividas_quitadas . "<br>";
    echo "- Total rendas: " . $total_rendas . "<br>";
    echo "- Quantidade rendas: " . $quantidade_rendas . "<br>";
    
    // Testar a transação completa
    echo "<h3>Testando transação completa...</h3>";
    $db->beginTransaction();
    
    // Inserir histórico de teste
    $query = "INSERT INTO historicos
        (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
         dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas)
        VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':mes_referencia', $mes_referencia . '-TEST');
    $stmt->bindParam(':total_dividas', $total_pendente + $total_quitado);
    $stmt->bindParam(':total_pendente', $total_pendente);
    $stmt->bindParam(':total_quitado', $total_quitado);
    $stmt->bindParam(':dividas_pendentes', $dividas_pendentes);
    $stmt->bindParam(':dividas_quitadas', $dividas_quitadas);
    $stmt->bindParam(':total_rendas', $total_rendas);
    $stmt->bindParam(':quantidade_rendas', $quantidade_rendas);
    
    if ($stmt->execute()) {
        echo "✓ Inserção de histórico OK<br>";
        $db->rollback(); // Desfazer teste
        echo "✓ Rollback OK<br>";
    } else {
        echo "❌ Erro na inserção: " . implode(', ', $stmt->errorInfo()) . "<br>";
        $db->rollback();
    }
    
    echo "<h2 style='color: green;'>✓ Todos os testes passaram! O problema pode estar em outro lugar.</h2>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO ENCONTRADO:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
