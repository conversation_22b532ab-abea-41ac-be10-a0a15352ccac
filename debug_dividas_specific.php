<?php
// Debug específico para dividas.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Específico - dividas.php</h1>";

// Simular exatamente o que acontece no dividas.php
try {
    echo "<h2>1. Incluindo arquivos...</h2>";
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    echo "✓ Arquivos incluídos com sucesso<br>";

    echo "<h2>2. Verificando login...</h2>";
    requireLogin();
    echo "✓ Login verificado - usuário logado<br>";

    echo "<h2>3. Criando conexão com banco...</h2>";
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    echo "✓ Conexão criada - User ID: $user_id<br>";

    echo "<h2>4. Testando query das dívidas...</h2>";
    $query = "SELECT * FROM dividas WHERE usuario_id = :user_id ORDER BY quitada ASC, data_vencimento ASC, data_criacao DESC";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $dividas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✓ Query das dívidas executada - Total: " . count($dividas) . " dívidas<br>";

    echo "<h2>5. Calculando totais...</h2>";
    $total_dividas = 0;
    $total_quitadas = 0;
    $total_pendentes = 0;
    $count_quitadas = 0;
    $count_pendentes = 0;

    foreach ($dividas as $divida) {
        $total_dividas += $divida['valor'];
        if ($divida['quitada']) {
            $total_quitadas += $divida['valor'];
            $count_quitadas++;
        } else {
            $total_pendentes += $divida['valor'];
            $count_pendentes++;
        }
    }
    echo "✓ Totais calculados:<br>";
    echo "&nbsp;&nbsp;- Total dívidas: R$ " . number_format($total_dividas, 2, ',', '.') . "<br>";
    echo "&nbsp;&nbsp;- Total quitadas: R$ " . number_format($total_quitadas, 2, ',', '.') . "<br>";
    echo "&nbsp;&nbsp;- Total pendentes: R$ " . number_format($total_pendentes, 2, ',', '.') . "<br>";

    echo "<h2>6. Testando include do header...</h2>";
    $page_title = 'Minhas Dívidas';
    ob_start();
    include 'includes/header.php';
    $header_output = ob_get_clean();
    echo "✓ Header incluído com sucesso<br>";

    echo "<h2>7. Testando processamento de POST...</h2>";
    if ($_POST) {
        echo "✓ Dados POST recebidos<br>";
        if (isset($_POST['action'])) {
            echo "✓ Ação identificada: " . $_POST['action'] . "<br>";
        }
    } else {
        echo "✓ Nenhum dado POST<br>";
    }

    echo "<h2>8. Testando fechamento mensal (simulação)...</h2>";
    try {
        // Simular a query de verificação de fechamento
        $query = "SELECT COUNT(*) as total FROM historicos WHERE usuario_id = :user_id AND mes_referencia = :mes_referencia";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':mes_referencia', date('Y-m'));
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $existe_fechamento = $result['total'] > 0;
        echo "✓ Verificação de fechamento OK - Existe: " . ($existe_fechamento ? 'Sim' : 'Não') . "<br>";
    } catch (Exception $e) {
        echo "⚠ Erro na verificação de fechamento: " . $e->getMessage() . "<br>";
    }

    echo "<h2 style='color: green;'>✓ Todos os testes passaram! dividas.php deve funcionar.</h2>";

} catch (Exception $e) {
    echo "<h2 style='color: red;'>✗ Erro encontrado:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
