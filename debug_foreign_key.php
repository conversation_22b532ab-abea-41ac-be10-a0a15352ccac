<?php
// Habilitar exibição de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debug Foreign Key - Tabela Historicos</h2>";

try {
    require_once 'includes/auth.php';
    require_once 'config/database.php';
    
    requireLogin();
    
    $database = new Database();
    $db = $database->getConnection();
    $user_id = $_SESSION['user_id'];
    
    echo "✓ Conexão estabelecida - User ID: " . $user_id . "<br>";
    
    // Verificar se tabela usuarios existe e sua estrutura
    echo "<h3>1. Verificando tabela usuarios</h3>";
    $query = "DESCRIBE usuarios";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $usuarios_structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Estrutura da tabela usuarios:<br>";
    foreach ($usuarios_structure as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")" . 
             ($column['Key'] == 'PRI' ? ' [PRIMARY KEY]' : '') . "<br>";
    }
    
    // Verificar se o usuário atual existe
    echo "<h3>2. Verificando se usuário existe</h3>";
    $query = "SELECT id, nome FROM usuarios WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($usuario) {
        echo "✓ Usuário encontrado: ID " . $usuario['id'] . " - " . $usuario['nome'] . "<br>";
    } else {
        echo "❌ Usuário não encontrado!<br>";
    }
    
    // Tentar criar tabela historicos sem foreign key primeiro
    echo "<h3>3. Criando tabela historicos sem foreign key</h3>";
    $db->exec("DROP TABLE IF EXISTS historicos");
    
    $create_table_sql = "CREATE TABLE historicos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        mes_referencia VARCHAR(7) NOT NULL,
        total_dividas DECIMAL(10,2) DEFAULT 0,
        total_pendente DECIMAL(10,2) DEFAULT 0,
        total_quitado DECIMAL(10,2) DEFAULT 0,
        dividas_pendentes INT DEFAULT 0,
        dividas_quitadas INT DEFAULT 0,
        total_rendas DECIMAL(10,2) DEFAULT 0,
        quantidade_rendas INT DEFAULT 0,
        data_fechamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $db->exec($create_table_sql);
    echo "✓ Tabela historicos criada sem foreign key<br>";
    
    // Testar inserção
    echo "<h3>4. Testando inserção</h3>";
    $mes_referencia = date('Y-m') . '-TEST';
    
    $query = "INSERT INTO historicos
        (usuario_id, mes_referencia, total_dividas, total_pendente, total_quitado,
         dividas_pendentes, dividas_quitadas, total_rendas, quantidade_rendas)
        VALUES (:user_id, :mes_referencia, :total_dividas, :total_pendente, :total_quitado,
                :dividas_pendentes, :dividas_quitadas, :total_rendas, :quantidade_rendas)";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':mes_referencia', $mes_referencia);
    $stmt->bindParam(':total_dividas', 100.00);
    $stmt->bindParam(':total_pendente', 50.00);
    $stmt->bindParam(':total_quitado', 50.00);
    $stmt->bindParam(':dividas_pendentes', 2);
    $stmt->bindParam(':dividas_quitadas', 1);
    $stmt->bindParam(':total_rendas', 1000.00);
    $stmt->bindParam(':quantidade_rendas', 3);
    
    if ($stmt->execute()) {
        echo "✓ Inserção realizada com sucesso<br>";
        
        // Verificar se foi inserido
        $query = "SELECT * FROM historicos WHERE mes_referencia = :mes_referencia";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':mes_referencia', $mes_referencia);
        $stmt->execute();
        $historico = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($historico) {
            echo "✓ Registro encontrado: ID " . $historico['id'] . "<br>";
            
            // Limpar teste
            $query = "DELETE FROM historicos WHERE mes_referencia = :mes_referencia";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':mes_referencia', $mes_referencia);
            $stmt->execute();
            echo "✓ Registro de teste removido<br>";
        }
    } else {
        echo "❌ Erro na inserção: " . implode(', ', $stmt->errorInfo()) . "<br>";
    }
    
    echo "<h2 style='color: green;'>✓ Teste concluído! A tabela historicos funciona sem foreign key.</h2>";
    echo "<p>Recomendação: Usar a tabela sem foreign key para evitar problemas.</p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ ERRO ENCONTRADO:</h2>";
    echo "<p><strong>Mensagem:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Arquivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Linha:</strong> " . $e->getLine() . "</p>";
}
?>
