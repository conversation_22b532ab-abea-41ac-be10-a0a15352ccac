<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - E1Dividas</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #FE7743;
            --secondary-color: #273F4F;
            --accent-color: #447D9B;
            --light-color: #D7D7D7;
        }
        
        body {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #e5663a;
            border-color: #e5663a;
        }
        
        .nav-tabs .nav-link {
            color: var(--secondary-color);
        }
        
        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
    </style>
</head>
<body>

<div class="test-card">
    <h2 class="text-center mb-4">
        <i class="fas fa-chart-line me-2" style="color: var(--secondary-color);"></i>
        Teste E1Dividas
    </h2>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        Esta é uma página de teste para verificar se CSS e JavaScript estão funcionando.
    </div>
    
    <!-- Teste de Tabs -->
    <ul class="nav nav-tabs mb-3" id="testTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="tab1" data-bs-toggle="tab" data-bs-target="#content1" type="button">
                <i class="fas fa-home me-2"></i>Tab 1
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tab2" data-bs-toggle="tab" data-bs-target="#content2" type="button">
                <i class="fas fa-user me-2"></i>Tab 2
            </button>
        </li>
    </ul>
    
    <div class="tab-content" id="testTabsContent">
        <div class="tab-pane fade show active" id="content1" role="tabpanel">
            <h5>Conteúdo da Tab 1</h5>
            <p>Se você consegue ver este conteúdo e as tabs funcionam, o Bootstrap está carregando corretamente.</p>
            
            <form id="testForm">
                <div class="mb-3">
                    <label for="testInput" class="form-label">Campo de Teste</label>
                    <input type="text" class="form-control" id="testInput" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-check me-2"></i>Testar Botão
                </button>
            </form>
        </div>
        
        <div class="tab-pane fade" id="content2" role="tabpanel">
            <h5>Conteúdo da Tab 2</h5>
            <p>Esta é a segunda tab. Se conseguir alternar entre as tabs, o JavaScript está funcionando.</p>
            
            <button class="btn btn-primary" onclick="showAlert()">
                <i class="fas fa-bell me-2"></i>Testar JavaScript
            </button>
        </div>
    </div>
    
    <hr class="my-4">
    
    <div class="text-center">
        <h6>Status dos Recursos:</h6>
        <div id="status">
            <span class="badge bg-secondary">Verificando...</span>
        </div>
        
        <div class="mt-3">
            <a href="login.php" class="btn btn-primary me-2">
                <i class="fas fa-arrow-left me-2"></i>Voltar ao Login
            </a>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Teste de JavaScript
function showAlert() {
    alert('JavaScript está funcionando corretamente!');
}

// Verificar status dos recursos
document.addEventListener('DOMContentLoaded', function() {
    const statusDiv = document.getElementById('status');
    let status = [];
    
    // Verificar Bootstrap CSS
    const testElement = document.createElement('div');
    testElement.className = 'btn btn-primary d-none';
    document.body.appendChild(testElement);
    const styles = window.getComputedStyle(testElement);
    
    if (styles.backgroundColor === 'rgb(254, 119, 67)') {
        status.push('<span class="badge bg-success me-1">CSS ✓</span>');
    } else {
        status.push('<span class="badge bg-danger me-1">CSS ✗</span>');
    }
    
    document.body.removeChild(testElement);
    
    // Verificar Bootstrap JS
    if (typeof bootstrap !== 'undefined') {
        status.push('<span class="badge bg-success me-1">Bootstrap JS ✓</span>');
    } else {
        status.push('<span class="badge bg-danger me-1">Bootstrap JS ✗</span>');
    }
    
    // Verificar Font Awesome
    const faTest = document.createElement('i');
    faTest.className = 'fas fa-home';
    faTest.style.display = 'none';
    document.body.appendChild(faTest);
    const faStyles = window.getComputedStyle(faTest, ':before');
    
    if (faStyles.content && faStyles.content !== 'none') {
        status.push('<span class="badge bg-success me-1">Font Awesome ✓</span>');
    } else {
        status.push('<span class="badge bg-danger me-1">Font Awesome ✗</span>');
    }
    
    document.body.removeChild(faTest);
    
    statusDiv.innerHTML = status.join('');
    
    // Teste do formulário
    document.getElementById('testForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const input = document.getElementById('testInput');
        if (input.value.trim()) {
            alert('Formulário funcionando! Valor: ' + input.value);
            input.value = '';
        } else {
            alert('Por favor, preencha o campo!');
        }
    });
});
</script>

</body>
</html>
