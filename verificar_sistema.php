<?php
/**
 * Script de verificação completa do sistema
 */

require_once 'config/database.php';

echo "<h1>🔍 Verificação Completa do Sistema E1Dividas</h1>";

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    echo "<div class='alert alert-danger'>❌ Falha na conexão com o banco de dados!</div>";
    exit;
}

echo "<div class='alert alert-success'>✅ Conexão com banco estabelecida!</div>";

// Verificar tabelas existentes
echo "<h2>📋 Tabelas do Sistema</h2>";
$tabelas_necessarias = [
    'usuarios' => 'Usuários do sistema',
    'dividas' => 'Dívidas cadastradas',
    'rendas' => 'Fontes de renda',
    'reserva_emergencia' => 'Reserva de emergência',
    'movimentacoes_reserva' => 'Movimentações da reserva'
];

$tabelas_existentes = [];
$query = "SHOW TABLES";
$stmt = $db->prepare($query);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_NUM);

foreach ($results as $row) {
    $tabelas_existentes[] = $row[0];
}

echo "<table class='table table-striped'>";
echo "<thead><tr><th>Tabela</th><th>Descrição</th><th>Status</th></tr></thead>";
echo "<tbody>";

foreach ($tabelas_necessarias as $tabela => $descricao) {
    $existe = in_array($tabela, $tabelas_existentes);
    $status = $existe ? "<span class='badge bg-success'>✅ Existe</span>" : "<span class='badge bg-danger'>❌ Não existe</span>";
    echo "<tr><td>$tabela</td><td>$descricao</td><td>$status</td></tr>";
}

echo "</tbody></table>";

// Verificar páginas do sistema
echo "<h2>📄 Páginas do Sistema</h2>";
$paginas = [
    'index.php' => 'Página inicial',
    'login.php' => 'Login/Registro',
    'dashboard.php' => 'Dashboard principal',
    'dividas.php' => 'Gestão de dívidas',
    'rendas.php' => 'Gestão de rendas',
    'reserva-emergencia.php' => 'Reserva de emergência',
    'includes/header.php' => 'Cabeçalho',
    'includes/footer.php' => 'Rodapé',
    'includes/auth.php' => 'Autenticação',
    'config/database.php' => 'Configuração do banco'
];

echo "<table class='table table-striped'>";
echo "<thead><tr><th>Arquivo</th><th>Descrição</th><th>Status</th><th>Ação</th></tr></thead>";
echo "<tbody>";

foreach ($paginas as $arquivo => $descricao) {
    $existe = file_exists($arquivo);
    $status = $existe ? "<span class='badge bg-success'>✅ Existe</span>" : "<span class='badge bg-danger'>❌ Não existe</span>";
    $acao = $existe ? "<a href='$arquivo' target='_blank' class='btn btn-sm btn-primary'>Testar</a>" : "-";
    echo "<tr><td>$arquivo</td><td>$descricao</td><td>$status</td><td>$acao</td></tr>";
}

echo "</tbody></table>";

// Verificar se há usuários cadastrados
echo "<h2>👥 Usuários do Sistema</h2>";
try {
    $query = "SELECT COUNT(*) as total FROM usuarios";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div class='alert alert-info'>";
    echo "Total de usuários cadastrados: <strong>" . $result['total'] . "</strong>";
    echo "</div>";
    
    if ($result['total'] == 0) {
        echo "<div class='alert alert-warning'>";
        echo "⚠️ Nenhum usuário cadastrado. <a href='login.php'>Cadastre-se aqui</a>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Erro ao verificar usuários: " . $e->getMessage() . "</div>";
}

// Ações recomendadas
echo "<h2>🔧 Ações Recomendadas</h2>";

$tabelas_faltando = array_diff(array_keys($tabelas_necessarias), $tabelas_existentes);

if (!empty($tabelas_faltando)) {
    echo "<div class='alert alert-warning'>";
    echo "<strong>⚠️ Tabelas em falta:</strong> " . implode(', ', $tabelas_faltando);
    echo "<br><a href='update_database.php' class='btn btn-warning mt-2'>Executar Atualização do Banco</a>";
    echo "</div>";
} else {
    echo "<div class='alert alert-success'>";
    echo "✅ Todas as tabelas necessárias estão presentes!";
    echo "</div>";
}

echo "<h2>🚀 Links Úteis</h2>";
echo "<div class='d-grid gap-2 d-md-block'>";
echo "<a href='dashboard.php' class='btn btn-primary'>Dashboard</a> ";
echo "<a href='reserva-emergencia.php' class='btn btn-success'>Reserva de Emergência</a> ";
echo "<a href='test_connection.php' class='btn btn-info'>Teste de Conexão</a> ";
echo "<a href='update_database.php' class='btn btn-warning'>Atualizar Banco</a>";
echo "</div>";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação do Sistema - E1Dividas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <!-- Conteúdo PHP será exibido aqui -->
    </div>
</body>
</html>
