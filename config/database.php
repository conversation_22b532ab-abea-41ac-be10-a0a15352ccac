<?php
/**
 * Configuração do Banco de Dados - E1Dividas
 */

class Database {
    private $host = 'srv1845.hstgr.io';
    private $db_name = 'u880879026_e1dividas';
    private $username = 'u880879026_usere1dividas';
    private $password = 'cmYw@n]2gH';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->exec("set names utf8");
        } catch(PDOException $exception) {
            echo "Erro de conexão: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}
?>